package com.qxl.phone.currencyconverter.util.vpages;

import android.annotation.SuppressLint;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.DiffUtil;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.ArrayList;
import java.util.List;

public class GenericFragmentAdapter<T> extends FragmentStateAdapter {
    private final List<T> items = new ArrayList<>();
    private final MultiFragmentCreator<T> fragmentCreator;
    private final @Nullable DiffCallbackProvider<T> diffCallback;

    public GenericFragmentAdapter(
            @NonNull FragmentActivity activity,
            @NonNull List<T> initialItems,
            @NonNull MultiFragmentCreator<T> fragmentCreator,
            @Nullable DiffCallbackProvider<T> diffCallback
    ) {
        super(activity);
        this.items.addAll(initialItems);
        this.fragmentCreator = fragmentCreator;
        this.diffCallback = diffCallback;
    }

    public GenericFragmentAdapter(
            @NonNull Fragment activity,
            @NonNull List<T> initialItems,
            @NonNull MultiFragmentCreator<T> fragmentCreator,
            @Nullable DiffCallbackProvider<T> diffCallback
    ) {
        super(activity);
        this.items.addAll(initialItems);
        this.fragmentCreator = fragmentCreator;
        this.diffCallback = diffCallback;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        return fragmentCreator.createFragment(items.get(position), position);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    @Override
    public long getItemId(int position) {
        return items.get(position).hashCode(); // 稳定 ID，保证 Fragment 恢复
    }

    @Override
    public boolean containsItem(long itemId) {
        for (T item : items) {
            if (item.hashCode() == itemId) return true;
        }
        return false;
    }

    /**
     * 提交新数据：如果提供 diffCallback 则使用差分，否则直接刷新
     */
    @SuppressLint("NotifyDataSetChanged")
    public void submitList(@NonNull List<T> newList) {
        if (diffCallback == null) {
            items.clear();
            items.addAll(newList);
            notifyDataSetChanged(); // fallback，刷新整个列表
            return;
        }

        final DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return items.size();
            }

            @Override
            public int getNewListSize() {
                return newList.size();
            }

            @Override
            public boolean areItemsTheSame(int oldItemPos, int newItemPos) {
                return diffCallback.areItemsTheSame(items.get(oldItemPos), newList.get(newItemPos));
            }

            @Override
            public boolean areContentsTheSame(int oldItemPos, int newItemPos) {
                return diffCallback.areContentsTheSame(items.get(oldItemPos), newList.get(newItemPos));
            }
        });

        items.clear();
        items.addAll(newList);
        diffResult.dispatchUpdatesTo(this);
    }
}
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/initiate_ivIcon"
        android:layout_width="89dp"
        android:layout_height="89dp"
        android:adjustViewBounds="true"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.385" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/sw_dp_20"
        android:text="@string/app_name"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sw_dp_20"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/initiate_ivIcon" />

    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/initiate_pbLoading"
        android:layout_width="289dp"
        android:layout_height="@dimen/sw_dp_6"
        android:indeterminate="false"
        android:max="100"
        app:indicatorColor="@color/color_1976D3"
        app:indicatorTrackGapSize="@dimen/sw_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.83"
        app:trackColor="@color/color_f0f7ff"
        app:trackCornerRadius="@dimen/sw_dp_100"
        app:trackStopIndicatorSize="@dimen/sw_dp_0"
        app:trackThickness="@dimen/sw_dp_8"
        tools:progress="50" />


    <FrameLayout
        android:id="@+id/splash_flContainer"
        android:layout_width="@dimen/sw_dp_0"
        android:layout_height="@dimen/sw_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
package com.qxl.phone.currencyconverter.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "favorite_currencies")
public class FavoriteCurrency {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private String currencyCode;
    private String currencyName;
    private String flag;
    private long timestamp;
    private double lastRate;
    private String baseCurrency;

    public FavoriteCurrency(String currencyCode, String currencyName, String flag, String baseCurrency) {
        this.id = 0;
        this.currencyCode = currencyCode;
        this.currencyName = currencyName;
        this.flag = flag;
        this.baseCurrency = baseCurrency;
        this.timestamp = System.currentTimeMillis();
    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyName() {
        return currencyName;
    }

    public void setCurrencyName(String currencyName) {
        this.currencyName = currencyName;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public double getLastRate() {
        return lastRate;
    }

    public void setLastRate(double lastRate) {
        this.lastRate = lastRate;
    }

    public String getBaseCurrency() {
        return baseCurrency;
    }

    public void setBaseCurrency(String baseCurrency) {
        this.baseCurrency = baseCurrency;
    }
} 
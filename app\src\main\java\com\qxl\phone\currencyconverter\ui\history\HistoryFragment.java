package com.qxl.phone.currencyconverter.ui.history;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.widget.LinearLayout;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.ui.history.adapter.HistoryAdapter;
import com.google.android.material.button.MaterialButton;
import java.util.ArrayList;
import com.qxl.phone.currencyconverter.ui.main.AppSharedViewModel;

public class HistoryFragment extends Fragment {
    private HistoryViewModel viewModel;
    private HistoryAdapter adapter;
    private RecyclerView recyclerView;
    private LinearLayout emptyLayout;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_history, container, false);
        recyclerView = view.findViewById(R.id.history_list);
        emptyLayout = view.findViewById(R.id.empty_history);
        MaterialButton btnClear = view.findViewById(R.id.btn_clear);

        adapter = new HistoryAdapter(requireContext(), new ArrayList<>());
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(adapter);

        viewModel = new ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory.getInstance(requireActivity().getApplication())).get(HistoryViewModel.class);
        viewModel.getHistoryListLiveData().observe(getViewLifecycleOwner(), list -> {
            System.out.println("[DEBUG] History list size: " + (list == null ? 0 : list.size()));
            adapter.updateData(list);
            if (list == null || list.isEmpty()) {
                recyclerView.setVisibility(View.GONE);
                emptyLayout.setVisibility(View.VISIBLE);
            } else {
                recyclerView.setVisibility(View.VISIBLE);
                emptyLayout.setVisibility(View.GONE);
            }
        });

        btnClear.setOnClickListener(v -> viewModel.clearHistory());

        AppSharedViewModel sharedViewModel = new ViewModelProvider(requireActivity()).get(AppSharedViewModel.class);
        sharedViewModel.getCacheCleared().observe(getViewLifecycleOwner(), cleared -> {
            if (Boolean.TRUE.equals(cleared)) {
                viewModel.clearHistory();
            }
        });

        sharedViewModel.getFavoriteChanged().observe(getViewLifecycleOwner(), changed -> {
            if (Boolean.TRUE.equals(changed)) {
                // If history UI depends on favorites, refresh here. Otherwise, no-op.
            }
        });

        sharedViewModel.getHistoryChanged().observe(getViewLifecycleOwner(), changed -> {
            if (Boolean.TRUE.equals(changed)) {
                // 触发历史数据刷新（LiveData 已自动推送，通常不需要手动刷新，但可调用 reload 方法以防万一）
                if (viewModel != null) {
                    // 如果有 reload 方法，调用；否则可留空
                    // viewModel.reloadHistory();
                }
            }
        });

        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        // 删除自动清空历史，避免每次进入页面都清空历史
        // if (viewModel != null) {
        //     viewModel.clearHistory();
        // }
    }
}
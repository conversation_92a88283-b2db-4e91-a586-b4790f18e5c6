package com.qxl.phone.currencyconverter.ui.list;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.Currency;
import com.qxl.phone.currencyconverter.model.FavoriteCurrency;
import com.qxl.phone.currencyconverter.data.AppDatabase;
import com.qxl.phone.currencyconverter.data.FavoriteDao;
import com.qxl.phone.currencyconverter.util.ExchangeRateApi;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;

public class CurrencyListViewModel extends AndroidViewModel {
    private final MutableLiveData<List<Currency>> currencyListLiveData = new MutableLiveData<>();
    private final MutableLiveData<Map<String, Double>> ratesLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<List<Currency>> filteredCurrencyList = new MutableLiveData<>();
    private final ExchangeRateApi api;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static final String BASE_CURRENCY = "CNY";
    private List<Currency> allCurrencyList;
    private FavoriteDao favoriteDao;
    private static final Map<String, String> CURRENCY_NAME_MAP = new HashMap<>();
    static {
        CURRENCY_NAME_MAP.put("USD", "US Dollar");
        CURRENCY_NAME_MAP.put("CNY", "Chinese Yuan");
        CURRENCY_NAME_MAP.put("EUR", "Euro");
        CURRENCY_NAME_MAP.put("JPY", "Japanese Yen");
        CURRENCY_NAME_MAP.put("GBP", "British Pound");
        CURRENCY_NAME_MAP.put("CAD", "Canadian Dollar");
        CURRENCY_NAME_MAP.put("AUD", "Australian Dollar");
        CURRENCY_NAME_MAP.put("CHF", "Swiss Franc");
        CURRENCY_NAME_MAP.put("HKD", "Hong Kong Dollar");
        CURRENCY_NAME_MAP.put("KRW", "South Korean Won");
        CURRENCY_NAME_MAP.put("SGD", "Singapore Dollar");
        CURRENCY_NAME_MAP.put("NZD", "New Zealand Dollar");
        CURRENCY_NAME_MAP.put("SEK", "Swedish Krona");
        CURRENCY_NAME_MAP.put("NOK", "Norwegian Krone");
        CURRENCY_NAME_MAP.put("DKK", "Danish Krone");
        CURRENCY_NAME_MAP.put("PLN", "Polish Zloty");
        CURRENCY_NAME_MAP.put("CZK", "Czech Koruna");
        CURRENCY_NAME_MAP.put("HUF", "Hungarian Forint");
        CURRENCY_NAME_MAP.put("RUB", "Russian Ruble");
        CURRENCY_NAME_MAP.put("TRY", "Turkish Lira");
        CURRENCY_NAME_MAP.put("BRL", "Brazilian Real");
        CURRENCY_NAME_MAP.put("MXN", "Mexican Peso");
        CURRENCY_NAME_MAP.put("INR", "Indian Rupee");
        CURRENCY_NAME_MAP.put("THB", "Thai Baht");
        CURRENCY_NAME_MAP.put("MYR", "Malaysian Ringgit");
        CURRENCY_NAME_MAP.put("IDR", "Indonesian Rupiah");
        CURRENCY_NAME_MAP.put("PHP", "Philippine Peso");
        CURRENCY_NAME_MAP.put("VND", "Vietnamese Dong");
        CURRENCY_NAME_MAP.put("TWD", "New Taiwan Dollar");
    }

    public CurrencyListViewModel(@NonNull Application application) {
        super(application);
        api = new ExchangeRateApi(application);
        AppDatabase database = AppDatabase.getInstance(application);
        favoriteDao = database.favoriteDao();
        
        fetchAllRatesAndCurrencies();
        scheduler.scheduleAtFixedRate(this::fetchAllRatesAndCurrencies, 15, 15, TimeUnit.MINUTES);
    }

    public LiveData<List<Currency>> getCurrencyListLiveData() {
        return currencyListLiveData;
    }

    public LiveData<Map<String, Double>> getRatesLiveData() {
        return ratesLiveData;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<List<Currency>> getFilteredCurrencyList() {
        return filteredCurrencyList;
    }

    public void searchCurrencies(String query) {
        if (allCurrencyList == null) return;

        List<Currency> filtered = new ArrayList<>();
        if (query == null || query.trim().isEmpty()) {
            filtered.addAll(allCurrencyList);
        } else {
            String lowerQuery = query.toLowerCase().trim();
            for (Currency currency : allCurrencyList) {
                if (currency.code.toLowerCase().contains(lowerQuery) ||
                        currency.englishName.toLowerCase().contains(lowerQuery)) {
                    filtered.add(currency);
                }
            }
        }
        filteredCurrencyList.postValue(filtered);
    }

    private void fetchAllRatesAndCurrencies() {
        api.getAllRatesToCNY(new ExchangeRateApi.AllRatesCallback() {
            @Override
            public void onSuccess(Map<String, Double> rates) {
                ratesLiveData.postValue(rates);

                Executors.newSingleThreadExecutor().execute(() -> {
                    List<Currency> currencyList = new ArrayList<>();
                    String[] mainCurrencies = {
                        "USD", "CNY", "EUR", "JPY", "GBP", "CAD", "AUD", "CHF", "HKD", "KRW",
                        "SGD", "NZD", "SEK", "NOK", "DKK", "PLN", "CZK", "HUF", "RUB", "TRY",
                        "BRL", "MXN", "INR", "THB", "MYR", "IDR", "PHP", "VND", "TWD"
                    };
                    
                    for (String code : mainCurrencies) {
                        if (rates.containsKey(code)) {
                            String name = CURRENCY_NAME_MAP.getOrDefault(code, code);
                            boolean isFavorite = favoriteDao.isFavorite(code);
                            currencyList.add(new Currency(code, name, isFavorite));
                        }
                    }
                    
                    allCurrencyList = currencyList;
                    currencyListLiveData.postValue(currencyList);
                    filteredCurrencyList.postValue(currencyList);
                });
            }
            @Override
            public void onError(String error) {
                errorMessage.postValue(error);
            }
        });
    }

    public void addToFavorites(Currency currency) {
        if (currency == null || currency.code == null) {
            errorMessage.postValue(getApplication().getString(R.string.invalid_currency_data));
            return;
        }
        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                if (!favoriteDao.isFavorite(currency.code)) {
                    FavoriteCurrency favorite = new FavoriteCurrency(
                        currency.code, 
                        currency.englishName,
                        "",
                        BASE_CURRENCY
                    );
                    favoriteDao.insertFavorite(favorite);
                }
            } catch (Exception e) {
                errorMessage.postValue(String.format(getApplication().getString(R.string.failed_to_add_favorites), e.getMessage()));
            }
        });
    }
    
    public void removeFromFavorites(Currency currency) {
        if (currency == null || currency.code == null) {
            errorMessage.postValue(getApplication().getString(R.string.invalid_currency_data));
            return;
        }
        Executors.newSingleThreadExecutor().execute(() -> {
            try {
                favoriteDao.deleteFavoriteByCode(currency.code);
            } catch (Exception e) {
                errorMessage.postValue(String.format(getApplication().getString(R.string.failed_to_remove_collection), e.getMessage()));
            }
        });
    }
    
    public boolean isFavorite(String currencyCode) {
        try {
            return favoriteDao.isFavorite(currencyCode);
        } catch (Exception e) {
            return false;
        }
    }
    
    public void refreshFavoriteStatus() {
        if (allCurrencyList != null) {
            Executors.newSingleThreadExecutor().execute(() -> {
                for (Currency currency : allCurrencyList) {
                    currency.favorite = favoriteDao.isFavorite(currency.code);
                }
                currencyListLiveData.postValue(allCurrencyList);
                filteredCurrencyList.postValue(allCurrencyList);
            });
        }
    }

    public void refreshRates() {
        fetchAllRatesAndCurrencies();
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        scheduler.shutdown();
    }
}
package com.qxl.phone.currencyconverter.ui.settings;

import android.app.Application;
import android.content.Context;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.data.AppDatabase;
import com.qxl.phone.currencyconverter.data.PreferencesHelper;
import com.qxl.phone.currencyconverter.util.Event;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class SettingsViewModel extends AndroidViewModel {
    
    private MutableLiveData<String> currentLanguage = new MutableLiveData<>();
    private MutableLiveData<Boolean> isDarkMode = new MutableLiveData<>();
    private MutableLiveData<Event<String>> message = new MutableLiveData<>();
    private ExecutorService executor = Executors.newSingleThreadExecutor();
    
    public SettingsViewModel(Application application) {
        super(application);
    }
    
    public LiveData<String> getCurrentLanguage() {
        return currentLanguage;
    }
    
    public LiveData<Boolean> getIsDarkMode() {
        return isDarkMode;
    }
    
    public LiveData<Event<String>> getMessage() {
        return message;
    }


    
    public void setDarkMode(boolean isDark) {
        Context context = getApplication();
        PreferencesHelper.setDarkMode(context, isDark);
        isDarkMode.setValue(isDark);
        message.setValue(new Event<>(isDark ? context.getString(R.string.dark_mode) + " " + context.getString(R.string.success) 
                               : context.getString(R.string.light_mode) + " " + context.getString(R.string.success)));
        androidx.appcompat.app.AppCompatDelegate.setDefaultNightMode(
            isDark ? androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_YES 
                   : androidx.appcompat.app.AppCompatDelegate.MODE_NIGHT_NO
        );
    }
    
    public void clearCache() {
        executor.execute(() -> {
            try {
                Context context = getApplication();
                context.getCacheDir().delete();
                AppDatabase.getInstance(context).historyDao().clearAll();
                AppDatabase.getInstance(context).favoriteDao().deleteAllFavorites();
                message.postValue(new Event<>(context.getString(R.string.cache_cleared)));
            } catch (Exception e) {
                message.postValue(new Event<>("Failed to empty cache: " + e.getMessage()));
            }
        });
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        executor.shutdown();
    }
}
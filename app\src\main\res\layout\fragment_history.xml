<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingTop="@dimen/sw_dp_30"
        android:paddingBottom="@dimen/sw_dp_60">

        <!-- 标题分区 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/sw_dp_24"
            android:paddingTop="@dimen/sw_dp_12"
            android:paddingBottom="@dimen/sw_dp_12">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <ImageView
                    android:layout_width="@dimen/sw_dp_28"
                    android:layout_height="@dimen/sw_dp_28"
                    android:src="@drawable/ic_history"
                    android:layout_marginEnd="@dimen/sw_dp_12"
                    app:tint="@color/primary" />
                <TextView
                    android:layout_width="@dimen/sw_dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/conversion_history"
                    android:textSize="@dimen/sw_dp_22"
                    android:textStyle="bold"
                    android:textColor="@color/on_surface" />
            </LinearLayout>
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/start_converting"
                android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/on_surface_variant"
                android:layout_marginTop="@dimen/sw_dp_4" />
            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/sw_dp_1"
                android:background="@color/outline_variant"
                android:layout_marginTop="@dimen/sw_dp_12"/>
        </LinearLayout>

        <!-- 历史记录列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/history_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_dp_0"
            android:layout_weight="1"
            android:layout_marginHorizontal="@dimen/sw_dp_16"
            android:layout_marginBottom="@dimen/sw_dp_8"
            android:clipToPadding="false"
            android:paddingTop="@dimen/sw_dp_8"
            android:paddingBottom="@dimen/sw_dp_8" />

        <!-- 空状态 -->
        <LinearLayout
            android:id="@+id/empty_history"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_dp_0"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:padding="@dimen/sw_dp_32">
            <ImageView
                android:layout_width="@dimen/sw_dp_80"
                android:layout_height="@dimen/sw_dp_80"
                android:src="@drawable/ic_history"
                android:alpha="0.5"
                android:layout_marginBottom="@dimen/sw_dp_16" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_history"
                android:textSize="@dimen/sw_dp_18"
                android:textColor="@color/on_surface_variant"
                android:gravity="center"
                android:layout_marginBottom="@dimen/sw_dp_8" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/start_converting"
                android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/on_surface_variant"
                android:gravity="center" />
        </LinearLayout>
    </LinearLayout>

    <!-- 悬浮底部按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_clear"
        android:layout_width="@dimen/sw_dp_200"
        android:layout_height="@dimen/sw_dp_40"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="@dimen/sw_dp_16"
        android:text="@string/clear_history"
        android:gravity="center"
        android:textSize="@dimen/sw_dp_14"
        android:textStyle="bold"
        android:letterSpacing="0.05"
        android:textColor="@color/on_primary"
        android:backgroundTint="@color/primary"
        app:cornerRadius="@dimen/sw_dp_20"
        app:elevation="@dimen/sw_dp_4"
        app:iconSize="@dimen/sw_dp_18"
        app:iconTint="@color/on_primary"
        app:iconGravity="textStart"
        app:iconPadding="@dimen/sw_dp_6"
        style="@style/Widget.Material3.Button" />

</FrameLayout>
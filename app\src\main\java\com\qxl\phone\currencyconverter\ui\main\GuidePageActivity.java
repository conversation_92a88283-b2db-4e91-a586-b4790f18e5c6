package com.qxl.phone.currencyconverter.ui.main;

import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import androidx.activity.EdgeToEdge;
import androidx.activity.OnBackPressedCallback;
import androidx.activity.SystemBarStyle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.data.enum2.GuidePages;
import com.qxl.phone.currencyconverter.databinding.ActivityGuidePageBinding;
import com.qxl.phone.currencyconverter.ui.base.BaseActivity;
import com.qxl.phone.currencyconverter.util.ActivityUtils;
import com.qxl.phone.currencyconverter.util.BarUtils;
import com.qxl.phone.currencyconverter.util.vpages.GenericFragmentAdapter;
import com.qxl.phone.currencyconverter.util.vpages.MultiFragmentCreator;


import java.util.List;

public class GuidePageActivity extends BaseActivity<ActivityGuidePageBinding> {
    private GenericFragmentAdapter<GuidePages> fragmentAdapter;

    @Nullable
    @Override
    public ActivityGuidePageBinding getViewBinding() {
        return ActivityGuidePageBinding.inflate(getLayoutInflater());
    }

    @Override
    public void initData(@Nullable Bundle extras) {
        super.initData(extras);
    }

    public void initWindow(@NonNull View rootView) {
        EdgeToEdge.enable(this, SystemBarStyle.light(Color.TRANSPARENT, Color.TRANSPARENT));

        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            final Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, 0, systemBars.right, systemBars.bottom);
            return insets;
        });
        BarUtils.hideNavBar(this);
    }

    @Override
    public void initView() {
        initWindow(binding.getRoot());
        final List<GuidePages> guidePages = List.of(GuidePages.values());
        fragmentAdapter = new GenericFragmentAdapter<>(this, guidePages, new MultiFragmentCreator<>() {
            @NonNull
            @Override
            public Fragment createFragment(@NonNull GuidePages item, int position) {
                return switch (item) {
                    case Guide1 -> new GuidePage1Fragment();
                    case Guide2 -> new GuidePage2Fragment();
                    case Guide3 -> new GuidePage3Fragment();
                };
            }
        }, null);

        binding.guidePageViewPager2.setAdapter(fragmentAdapter);
        binding.guidePageViewPager2.setUserInputEnabled(true);
        binding.guidePageViewPager2.setOffscreenPageLimit(fragmentAdapter.getItemCount());
    }

    @Override
    public void initEvent() {
        getOnBackPressedDispatcher().addCallback(new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
            }
        });

        binding.guidePageTvNext.setOnClickListener(v -> jumpNextPage());
        binding.guidePageTvSkip.setOnClickListener(view -> {
            jumpToMainPage(false);
        });

        binding.guidePageViewPager2.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                final boolean isLastPage = position == fragmentAdapter.getItemCount() - 1;
                if (position == 0) {
                    binding.guidePageIvNav.setImageResource(R.drawable.guide_indicator_img_1);
                } else if (position == 1) {
                    binding.guidePageIvNav.setImageResource(R.drawable.guide_indicator_img_2);
                } else if (isLastPage) {
                    binding.guidePageIvNav.setImageResource(R.drawable.guide_indicator_img_3);
                }
            }
        });
    }


    private void jumpNextPage() {
        int itemCount = binding.guidePageViewPager2.getCurrentItem();
        if (itemCount < fragmentAdapter.getItemCount() - 1) {
            binding.guidePageViewPager2.setCurrentItem(++itemCount);
        } else {
            jumpToMainPage(false);
        }
    }

    /**
     * 跳转主页
     */
    private void jumpToMainPage(boolean isAdvertShowed) {
        ActivityUtils.startActivity(MainActivity.class);
        ActivityUtils.finishActivity(this, !isAdvertShowed);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

    }
}

package com.qxl.phone.currencyconverter.data;

import android.content.Context;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import com.qxl.phone.currencyconverter.model.Currency;
import com.qxl.phone.currencyconverter.model.ExchangeRate;
import com.qxl.phone.currencyconverter.model.ConversionHistory;
import com.qxl.phone.currencyconverter.model.FavoriteCurrency;

@Database(entities = {Currency.class, ExchangeRate.class, ConversionHistory.class, FavoriteCurrency.class}, version = 3, exportSchema = false)
public abstract class AppDatabase extends RoomDatabase {
    public abstract CurrencyDao currencyDao();
    public abstract HistoryDao historyDao();
    public abstract FavoriteDao favoriteDao();

    private static volatile AppDatabase INSTANCE;

    public static AppDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(context.getApplicationContext(),
                                    AppDatabase.class, "currency_converter_db")
                            .fallbackToDestructiveMigration()
                            .allowMainThreadQueries()
                            .build();
                }
            }
        }
        return INSTANCE;
    }
}
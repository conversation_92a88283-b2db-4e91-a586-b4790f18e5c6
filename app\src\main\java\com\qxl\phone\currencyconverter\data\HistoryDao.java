package com.qxl.phone.currencyconverter.data;

import androidx.room.*;
import com.qxl.phone.currencyconverter.model.ConversionHistory;
import java.util.List;
import androidx.lifecycle.LiveData;

@Dao
public interface HistoryDao {
    @Query("SELECT * FROM conversion_history ORDER BY timestamp DESC")
    List<ConversionHistory> getAll();

    @Query("SELECT * FROM conversion_history ORDER BY timestamp DESC")
    LiveData<List<ConversionHistory>> getAllLive();

    @Insert
    void insert(ConversionHistory history);

    @Query("DELETE FROM conversion_history")
    void clearAll();
}
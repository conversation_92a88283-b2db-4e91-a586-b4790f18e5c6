package com.qxl.phone.currencyconverter.ui.settings;


import android.app.Activity;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;


import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.data.LanguageEntity;
import com.qxl.phone.currencyconverter.data.enum2.Language;
import com.qxl.phone.currencyconverter.databinding.ActivityLanguageBinding;
import com.qxl.phone.currencyconverter.language.MultiLanguages;
import com.qxl.phone.currencyconverter.ui.base.BaseActivity;
import com.qxl.phone.currencyconverter.ui.base.BaseAdapter;
import com.qxl.phone.currencyconverter.ui.main.GuidePageActivity;
import com.qxl.phone.currencyconverter.ui.main.MainActivity;
import com.qxl.phone.currencyconverter.util.ActivityUtils;
import com.qxl.phone.currencyconverter.util.CacheUtils;
import com.qxl.phone.currencyconverter.util.LifecycleUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class LanguageActivity extends BaseActivity<ActivityLanguageBinding> implements BaseAdapter.OnItemClickListener {

    private LanguageEntity curLanguageEntity;
    private LanguageAdapter languageAdapter = new LanguageAdapter(this);
    ;
    private boolean isFromSetting = false;
    public static final String IS_FROM_SETTING = "IS_FROM_SETTING";
    private CountDownTimer countDownTimer;

    @Nullable
    @Override
    public ActivityLanguageBinding getViewBinding() {
        return ActivityLanguageBinding.inflate(getLayoutInflater());
    }

    @Override
    public void initData(@Nullable Bundle extras) {

        if (extras != null) {
            isFromSetting = extras.getBoolean(IS_FROM_SETTING, false);
        }
        final String lastLanguageName = CacheUtils.getCurLanguageName();
        if (!TextUtils.isEmpty(lastLanguageName)) {
            curLanguageEntity = new LanguageEntity(lastLanguageName, true);
        }
    }


    @Override
    public void initView() {

        binding.ivBack.setVisibility(isFromSetting ? View.VISIBLE : View.INVISIBLE);
        languageAdapter.setOnItemClickListener(LanguageActivity.this);
        binding.languageActRv.setAdapter(languageAdapter);

        final RecyclerView.ItemAnimator itemAnimator = binding.languageActRv.getItemAnimator();
        if (itemAnimator instanceof SimpleItemAnimator) {
            ((SimpleItemAnimator) itemAnimator).setSupportsChangeAnimations(false);
        }
    }

    @Override
    public void initEvent() {
        super.initEvent();
        binding.languageActTvDone.setOnClickListener(view -> {
            if (CacheUtils.isFirstLanguageDoneUse()) {
                CacheUtils.updateFirstLanguageShow(false);
            }
            updateLanguage();
        });
        binding.ivBack.setOnClickListener(view -> onPageBack());
    }

    @Override
    public void loadData() {
        super.loadData();
        loadLanguageList();
    }

    private void loadLanguageList() {
        final List<LanguageEntity> list = Arrays.stream(Language.values())
                .map(item -> new LanguageEntity(item.name(), isLanguageSelected(item)))
                .collect(Collectors.toCollection(ArrayList::new));

        languageAdapter.setData(list);
    }

    private boolean isLanguageSelected(@NonNull Language language) {
        return curLanguageEntity != null && Objects.equals(language.name(), curLanguageEntity.getLanguageName());
    }

    private void updateLanguage() {
        LanguageEntity languageEntity = curLanguageEntity;
        if (languageEntity == null) {
            languageEntity = new LanguageEntity(Language.DEFAULT.name(), true);
        }
        final String lastLanguageName = CacheUtils.getCurLanguageName();
        CacheUtils.setCurLanguageName(languageEntity.getLanguageName());

        Language language = Language.valueOf(languageEntity.getLanguageName());
        if (language.equals(Language.DEFAULT)) {
            MultiLanguages.clearAppLanguage(this);
        } else {
            MultiLanguages.setAppLanguage(this, language.getLocale());
        }
        if (!isFromSetting) {
            ActivityUtils.startActivity(GuidePageActivity.class);
            ActivityUtils.finishActivity(this);
            return;
        }
        // 跟上次同语种无需重启
        if (Objects.equals(lastLanguageName, languageEntity.getLanguageName())) {
            ActivityUtils.finishActivity(this);
            return;
        }
        // 先关闭其他的 Activity
        for (Activity activity : ActivityUtils.getActivityList()) {
            if (activity == this) continue;
            ActivityUtils.finishActivity(activity, false);
        }
        ActivityUtils.startActivity(MainActivity.class);
        overridePendingTransition(
                R.anim.activity_alpha_in, R.anim.activity_alpha_out
        );

        ActivityUtils.finishActivity(this);
    }

    @Override
    public void onItemClick(RecyclerView recyclerView, View itemView, int position) {
        LifecycleUtils.launch(this, () -> {
            final LanguageEntity entity = languageAdapter.updateSelect(position);
            if (entity == null) {
                return;
            }
            curLanguageEntity = entity;
        });
    }


    private void onPageBack() {
        ActivityUtils.finishActivity(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
            countDownTimer = null;
        }
    }

}

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:paddingTop="@dimen/sw_dp_30">
    <!-- 标题分区 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/sw_dp_24"
        android:paddingTop="@dimen/sw_dp_12"
        android:paddingBottom="@dimen/sw_dp_12">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/sw_dp_28"
                android:layout_height="@dimen/sw_dp_28"
                android:src="@drawable/ic_currency_list"
                app:tint="@color/primary"
                android:layout_marginEnd="@dimen/sw_dp_12" />

            <TextView
                android:layout_width="@dimen/sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/all_currencies"
                android:textSize="@dimen/sw_dp_22"
                android:textStyle="bold"
                android:textColor="@color/on_surface" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/currencies_list_desc"
            android:textSize="@dimen/sw_dp_14"
            android:textColor="@color/on_surface_variant"
            android:layout_marginTop="@dimen/sw_dp_4" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_dp_1"
            android:background="@color/outline_variant"
            android:layout_marginTop="@dimen/sw_dp_12"/>
    </LinearLayout>

    <!-- 搜索栏卡片 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/sw_dp_16"
        app:cardCornerRadius="@dimen/sw_dp_12"
        app:cardElevation="@dimen/sw_dp_2"
        app:cardBackgroundColor="@color/surface">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/search_currency"
            app:startIconDrawable="@drawable/ic_search"
            app:startIconTint="@color/primary"
            app:endIconMode="clear_text"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/search_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 货币列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_dp_0"
        android:layout_weight="1"
        android:layout_marginHorizontal="@dimen/sw_dp_16"
        android:layout_marginBottom="@dimen/sw_dp_16">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/currency_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingTop="@dimen/sw_dp_8"
            android:paddingBottom="@dimen/sw_dp_8" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/empty_state"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_dp_0"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        android:padding="@dimen/sw_dp_32">

        <ImageView
            android:layout_width="@dimen/sw_dp_80"
            android:layout_height="@dimen/sw_dp_80"
            android:src="@drawable/ic_empty_search"
            android:alpha="0.5"
            android:layout_marginBottom="@dimen/sw_dp_16" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_currencies_found"
            android:textSize="@dimen/sw_dp_18"
            android:textColor="@color/on_surface_variant"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
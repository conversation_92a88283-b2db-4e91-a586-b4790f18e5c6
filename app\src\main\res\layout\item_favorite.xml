<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/sw_dp_8"
    app:cardCornerRadius="@dimen/sw_dp_12"
    app:cardElevation="@dimen/sw_dp_2"
    app:cardBackgroundColor="@color/surface"
    android:foreground="@null"
    android:clickable="false"
    android:focusable="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/sw_dp_16"
        android:gravity="center_vertical">

        <!-- 货币信息 -->
        <LinearLayout
            android:layout_width="@dimen/sw_dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/sw_dp_4">

                <TextView
                    android:id="@+id/currency_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="USD"
                    android:textSize="@dimen/sw_dp_18"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:background="@drawable/currency_code_background"
                    android:paddingHorizontal="@dimen/sw_dp_8"
                    android:paddingVertical="@dimen/sw_dp_4"
                    android:layout_marginEnd="@dimen/sw_dp_8" />

                <TextView
                    android:id="@+id/currency_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="US Dollar"
                    android:textSize="@dimen/sw_dp_16"
                    android:textColor="@color/on_surface" />

            </LinearLayout>

            <TextView
                android:id="@+id/exchange_rate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1 CNY = 0.1389 USD"
                android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/on_surface_variant"
                android:layout_marginBottom="@dimen/sw_dp_4" />

            <TextView
                android:id="@+id/last_updated"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add time: 2024-01-01 12:00"
                android:textSize="@dimen/sw_dp_12"
                android:textColor="@color/on_surface_variant" />

        </LinearLayout>

        <!-- 移除按钮 -->
        <ImageView
            android:id="@+id/remove_favorite"
            android:layout_width="@dimen/sw_dp_40"
            android:layout_height="@dimen/sw_dp_40"
            android:src="@drawable/ic_clear"
            app:tint="@color/error"
            android:padding="@dimen/sw_dp_4"
            android:contentDescription="@string/remove_from_favorites" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView> 
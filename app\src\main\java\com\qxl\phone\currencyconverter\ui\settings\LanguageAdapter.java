package com.qxl.phone.currencyconverter.ui.settings;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.data.LanguageEntity;
import com.qxl.phone.currencyconverter.data.enum2.Language;
import com.qxl.phone.currencyconverter.ui.base.AppAdapter;
import com.qxl.phone.currencyconverter.ui.base.BaseAdapter;


public class LanguageAdapter extends AppAdapter<LanguageEntity> {

    public LanguageAdapter(Context context) {
        super(context);
    }
    private int lastPos = -1;
    @Nullable
    public LanguageEntity updateSelect(int position) {
        // 如果选中的位置和上次相同，返回
        if (lastPos == position) return null;

        // 如果 lastPos 为 -1，表示尚未选中任何项，从数据中找到第一个选中的项
        if (lastPos == -1) {
            lastPos = findFirstSelectedIndex();  // 查找第一个选中的项的索引
        }

        // 取消上次选中的项
        if (lastPos >= 0 && lastPos < getData().size()) {
            final LanguageEntity entity = getData().get(lastPos);
            entity.setSelect(false);  // 取消选中
            notifyItemChanged(lastPos);
        }

        // 选中当前位置的项
        if (position >= 0 && position < getData().size()) {
            final LanguageEntity entity = getData().get(position);
            entity.setSelect(true);  // 设置为选中
            lastPos = position;  // 更新 lastPos 为当前选中的位置
            notifyItemChanged(position);
            return entity;
        }

        return null;
    }
    // 查找第一个选中的项的索引
    private int findFirstSelectedIndex() {
        for (int i = 0; i < getData().size(); i++) {
            if (getData().get(i).isSelect()) {
                return i;
            }
        }
        return -1;  // 如果没有找到选中的项，返回 -1
    }

    @NonNull
    @Override
    public BaseAdapter<?>.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder();
    }
    private final class ViewHolder extends AppAdapter<?>.ViewHolder {

        private AppCompatTextView languageTvName;
        private AppCompatImageView languageIvSelect;
        private ConstraintLayout languageFlContainer;

        private ViewHolder() {
            super(R.layout.item_language);
            languageTvName = findViewById(R.id.language_tvName);
            languageIvSelect = findViewById(R.id.language_ivSelect);
            languageFlContainer = findViewById(R.id.language_flContainer);
        }

        @Override
        public void onBindView(int position) {
            LanguageEntity languageEntity = getItem(position);
            Language language = Language.valueOf(languageEntity.getLanguageName());
            languageTvName.setText(language.getStringRes());
            languageIvSelect.setVisibility(languageEntity.isSelect()?View.VISIBLE:View.INVISIBLE);
            if(languageEntity.isSelect()){
                languageFlContainer.setBackground(getDrawable(R.drawable.shape_r8_1976d3));
            }else {
                languageFlContainer.setBackground(getDrawable(R.drawable.shape_r8_ffffff));
            }
        }
    }
}

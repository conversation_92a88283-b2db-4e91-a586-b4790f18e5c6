package com.qxl.phone.currencyconverter.ui.main;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.qxl.phone.currencyconverter.databinding.FragmentGuidePage2Binding;
import com.qxl.phone.currencyconverter.ui.base.BaseFragment;


public class GuidePage2Fragment extends BaseFragment<FragmentGuidePage2Binding> {
    @NonNull
    @Override
    public FragmentGuidePage2Binding initBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container) {
        return FragmentGuidePage2Binding.inflate(inflater, container, false);
    }

    @Override
    public void initData(@Nullable Bundle arguments) {
    }
}
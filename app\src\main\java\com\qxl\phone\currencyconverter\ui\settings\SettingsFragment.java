package com.qxl.phone.currencyconverter.ui.settings;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.data.AppConstant;
import com.qxl.phone.currencyconverter.data.PreferencesHelper;
import com.qxl.phone.currencyconverter.ui.main.MainActivity;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.card.MaterialCardView;
import com.qxl.phone.currencyconverter.util.Event;
import com.qxl.phone.currencyconverter.ui.main.AppSharedViewModel;
import android.widget.LinearLayout;

public class SettingsFragment extends Fragment {
    
    private SettingsViewModel viewModel;
    private AutoCompleteTextView languageSpinner;
    private SwitchMaterial themeSwitch;
    private MaterialCardView clearCacheCard;
    private MaterialCardView privacyPolicyCard;
    private LinearLayout languageLayout;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_settings, container, false);

        themeSwitch = view.findViewById(R.id.switch_theme);
        clearCacheCard = view.findViewById(R.id.card_clear_cache);
        privacyPolicyCard = view.findViewById(R.id.card_privacy_policy);
        languageLayout = view.findViewById(R.id.layout_language);

        setupThemeSwitch();

        clearCacheCard.setOnClickListener(v -> showClearCacheConfirmDialog());
        privacyPolicyCard.setOnClickListener(v -> openUrl(AppConstant.PRIVACY_POLICY_URI));
        languageLayout.setOnClickListener(v -> openLanguageActivity());
        
        return view;
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        viewModel = new ViewModelProvider(this).get(SettingsViewModel.class);
        AppSharedViewModel sharedViewModel = new ViewModelProvider(requireActivity()).get(AppSharedViewModel.class);

        viewModel.getMessage().observe(getViewLifecycleOwner(), event -> {
            if (event != null) {
                String message = event.getContentIfNotHandled();
                if (message != null && !message.isEmpty()) {
                    Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
                    if (message.equals(getString(R.string.cache_cleared))) {
                        sharedViewModel.notifyCacheCleared();
                    }
                }
            }
        });
    }

    
    private void setupThemeSwitch() {
        themeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            viewModel.setDarkMode(isChecked);
        });
    }
    
    private void showClearCacheConfirmDialog() {
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.clear_cache))
                .setMessage(getString(R.string.clear_cache_confirm))
                .setPositiveButton(getString(R.string.clear_cache), (dialog, which) -> {
                    viewModel.clearCache();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }
    private void openUrl(String url) {
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        startActivity(intent);
    }

    private void openLanguageActivity() {
        Intent intent = new Intent(getContext(), LanguageActivity.class);
        intent.putExtra(LanguageActivity.IS_FROM_SETTING, true);
        startActivity(intent);
    }
}
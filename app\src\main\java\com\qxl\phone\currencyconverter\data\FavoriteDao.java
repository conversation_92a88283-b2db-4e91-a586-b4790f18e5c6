package com.qxl.phone.currencyconverter.data;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.qxl.phone.currencyconverter.model.FavoriteCurrency;

import java.util.List;

@Dao
public interface FavoriteDao {

    @Query("SELECT * FROM favorite_currencies ORDER BY timestamp DESC")
    List<FavoriteCurrency> getAll();

    @Query("SELECT * FROM favorite_currencies ORDER BY timestamp DESC")
    LiveData<List<FavoriteCurrency>> getAllFavorites();
    
    @Query("SELECT * FROM favorite_currencies WHERE currencyCode = :currencyCode LIMIT 1")
    FavoriteCurrency getFavoriteByCode(String currencyCode);
    
    @Query("SELECT EXISTS(SELECT 1 FROM favorite_currencies WHERE currencyCode = :currencyCode)")
    boolean isFavorite(String currencyCode);
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertFavorite(FavoriteCurrency favorite);
    
    @Update
    void updateFavorite(FavoriteCurrency favorite);
    
    @Delete
    void deleteFavorite(FavoriteCurrency favorite);
    
    @Query("DELETE FROM favorite_currencies WHERE currencyCode = :currencyCode")
    void deleteFavoriteByCode(String currencyCode);
    
    @Query("DELETE FROM favorite_currencies")
    void deleteAllFavorites();
    
    @Query("SELECT COUNT(*) FROM favorite_currencies")
    int getFavoriteCount();

    @Query("UPDATE favorite_currencies SET lastRate = :rate WHERE currencyCode = :currencyCode")
    void updateLastRate(String currencyCode, double rate);

    @Query("SELECT * FROM favorite_currencies ORDER BY timestamp DESC")
    LiveData<List<FavoriteCurrency>> getAllLive();
} 
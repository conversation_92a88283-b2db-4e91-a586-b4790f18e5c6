apply {
    from "${rootDir.path}/config/gradle/app.gradle"
    from "android_guard.gradle"
}

android {
    namespace Android.applicationId

    defaultConfig {
        if (Options.IS_PUBLISH) {
            manifestPlaceholders.put("ADMOB_APP_ID", Options.ADMOB_ID)

            println()
            println("ADMOB_APP_ID RELEASE ============ >> ")
            println("${Options.ADMOB_ID}")
        } else {
            manifestPlaceholders.put("ADMOB_APP_ID", Options.ADMOB_ID_TEST)

            println()
            println("ADMOB_APP_ID DEBUG ============ >> ")
            println("${Options.ADMOB_ID_TEST}")
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.aar', '*.jar'], dir: 'libs')

    implementation Deps.core_ktx
    implementation Deps.appcompat
    implementation Deps.material
    implementation Deps.constraintlayout
    implementation Deps.recyclerview
    implementation Deps.viewpager2
    implementation Deps.swiperefreshlayout

    implementation Deps.room_runtime_android
    implementation Deps.room_common_jvm
    kapt Deps.room_compiler

    implementation Deps.okhttp

    implementation "com.google.android.gms:play-services-ads:24.0.0"
}
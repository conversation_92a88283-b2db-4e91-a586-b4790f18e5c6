package com.qxl.phone.currencyconverter.ui.main;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.os.Bundle;
import android.view.animation.DecelerateInterpolator;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.Nullable;

import com.qxl.phone.currencyconverter.databinding.ActivitySplashBinding;
import com.qxl.phone.currencyconverter.ui.base.BaseActivity;
import com.qxl.phone.currencyconverter.ui.settings.LanguageActivity;
import com.qxl.phone.currencyconverter.util.ActivityUtils;
import com.qxl.phone.currencyconverter.util.CacheUtils;


public class MySplashActivity extends BaseActivity<ActivitySplashBinding> {

    @Nullable
    private ValueAnimator pbAnimator;

    @Nullable
    @Override
    public ActivitySplashBinding getViewBinding() {
        return ActivitySplashBinding.inflate(getLayoutInflater());
    }

    @Override
    public void initData(@Nullable Bundle extras) {
        super.initData(extras);
    }

    @Override
    public void loadData() {
        startInitialization();
        if (CacheUtils.isFirstLanguageShow()) {
            ActivityUtils.startActivity(LanguageActivity.class);
            ActivityUtils.finishActivity(this);
        } else {
            ActivityUtils.startActivity(MainActivity.class);
        }
    }

    @Override
    public void initEvent() {
        super.initEvent();
        getOnBackPressedDispatcher().addCallback(new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // 物理返回拦截
            }
        });
    }


    private void startInitialization() {
        updateProgress(100, 5 * 1000);
    }

    /**
     * 更新进度条
     *
     * @noinspection SameParameterValue
     */
    private void updateProgress(int endProgress, long duration) {
        cancelProgressAnimator();

        final int maxProgress = binding.initiatePbLoading.getMax();
        final int startProgress = Math.max(0, Math.min(binding.initiatePbLoading.getProgress(), maxProgress));
        final int end = Math.max(0, Math.min(endProgress, maxProgress));

        pbAnimator = ObjectAnimator.ofInt(startProgress, end);
        pbAnimator.setDuration(duration);
        pbAnimator.setInterpolator(new DecelerateInterpolator());
        pbAnimator.addUpdateListener(animation -> {
            int animatedProgress = (int) animation.getAnimatedValue();
            animatedProgress = Math.max(0, Math.min(animatedProgress, maxProgress)); // 再保护一次
            binding.initiatePbLoading.setProgress(animatedProgress);
        });

        pbAnimator.start();
    }

    private void cancelProgressAnimator() {
        if (pbAnimator != null && pbAnimator.isRunning()) {
            pbAnimator.cancel();
            pbAnimator = null;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        cancelProgressAnimator();
    }
}

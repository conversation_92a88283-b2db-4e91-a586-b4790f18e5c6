package com.qxl.phone.currencyconverter.ui.main;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;

import com.qxl.phone.currencyconverter.ui.base.BaseActivity;

public class FixActivity extends BaseActivity<ViewBinding> {


    @Override
    protected ViewBinding getViewBinding() {
        return null;
    }

    @Override
    public void initData(@Nullable Bundle extras) {
        super.initData(extras);
        final Intent intent = new Intent(this, MySplashActivity.class);
        startActivity(intent);
        finish();
    }

}

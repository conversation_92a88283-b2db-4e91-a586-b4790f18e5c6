<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/sw_dp_8"
    android:layout_marginVertical="@dimen/sw_dp_4"
    app:cardCornerRadius="@dimen/sw_dp_12"
    app:cardElevation="@dimen/sw_dp_2"
    app:cardBackgroundColor="@color/surface"
    android:foreground="@null"
    android:clickable="false"
    android:focusable="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/sw_dp_16">

        <!-- 转换信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/sw_dp_8">

            <!-- 从货币 -->
            <LinearLayout
                android:layout_width="@dimen/sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/text_from_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sw_dp_18"
                    android:textStyle="bold"
                    android:textColor="@color/on_surface"
                    android:layout_marginBottom="@dimen/sw_dp_4" />

                <TextView
                    android:id="@+id/text_from"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sw_dp_14"
                    android:textColor="@color/on_surface_variant" />

            </LinearLayout>

            <!-- 转换箭头 -->
            <ImageView
                android:layout_width="@dimen/sw_dp_24"
                android:layout_height="@dimen/sw_dp_24"
                android:src="@drawable/ic_arrow_forward"
                android:layout_marginHorizontal="@dimen/sw_dp_16"
                app:tint="@color/primary" />

            <!-- 到货币 -->
            <LinearLayout
                android:layout_width="@dimen/sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/text_to_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sw_dp_18"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="@dimen/sw_dp_4" />

                <TextView
                    android:id="@+id/text_to"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/sw_dp_14"
                    android:textColor="@color/on_surface_variant" />

            </LinearLayout>

        </LinearLayout>

        <!-- 时间信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="@dimen/sw_dp_16"
                android:layout_height="@dimen/sw_dp_16"
                android:src="@drawable/ic_time"
                android:tint="@color/on_surface_variant"
                android:layout_marginEnd="@dimen/sw_dp_8"
                tools:ignore="UseAppTint" />

            <TextView
                android:id="@+id/text_time"
                android:layout_width="@dimen/sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="@dimen/sw_dp_12"
                android:textColor="@color/on_surface_variant" />

            <!-- 汇率信息 -->
            <TextView
                android:id="@+id/text_rate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sw_dp_12"
                android:textColor="@color/primary"
                android:background="@drawable/rate_badge"
                android:paddingHorizontal="@dimen/sw_dp_8"
                android:paddingVertical="@dimen/sw_dp_4" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
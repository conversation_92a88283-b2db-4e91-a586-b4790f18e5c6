package com.qxl.phone.currencyconverter.ui.favorite;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.ui.favorite.adapter.FavoriteAdapter;
import com.qxl.phone.currencyconverter.model.FavoriteCurrency;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.qxl.phone.currencyconverter.ui.main.AppSharedViewModel;

public class FavoriteFragment extends Fragment {

    private FavoriteViewModel viewModel;
    private FavoriteAdapter adapter;
    private RecyclerView recyclerView;
    private TextView favoriteCount;
    private View emptyView;
    private SwipeRefreshLayout swipeRefreshLayout;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_favorite, container, false);

        recyclerView = view.findViewById(R.id.favorite_list);
        favoriteCount = view.findViewById(R.id.favorite_count);
        emptyView = view.findViewById(R.id.empty_favorites);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh);

        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new FavoriteAdapter();
        recyclerView.setAdapter(adapter);

        swipeRefreshLayout.setOnRefreshListener(() -> {
            viewModel.updateRates();
        });

        adapter.setOnFavoriteClickListener(new FavoriteAdapter.OnFavoriteClickListener() {
            @Override
            public void onFavoriteClick(FavoriteCurrency favorite) {
                if (getActivity() instanceof com.qxl.phone.currencyconverter.ui.main.MainActivity) {
                    ((com.qxl.phone.currencyconverter.ui.main.MainActivity) getActivity())
                        .showConversionFragment(favorite.getCurrencyCode());
                }
            }

            @Override
            public void onRemoveFavorite(FavoriteCurrency favorite) {
                showRemoveConfirmDialog(favorite);
            }
        });

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        viewModel = new ViewModelProvider(this).get(FavoriteViewModel.class);
        AppSharedViewModel sharedViewModel = new ViewModelProvider(requireActivity()).get(AppSharedViewModel.class);
        sharedViewModel.getCacheCleared().observe(getViewLifecycleOwner(), cleared -> {
            if (Boolean.TRUE.equals(cleared)) {
                viewModel.clearAllFavorites();
            }
        });

        viewModel.getFavoriteListLiveData().observe(getViewLifecycleOwner(), favorites -> {
            adapter.setFavorites(favorites);
            updateUI(favorites);
        });

        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            swipeRefreshLayout.setRefreshing(isLoading);
        });

        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(getContext(), error, Toast.LENGTH_LONG).show();
            }
        });

        adapter.setOnFavoriteClickListener(new FavoriteAdapter.OnFavoriteClickListener() {
            @Override
            public void onFavoriteClick(FavoriteCurrency favorite) {
                if (getActivity() instanceof com.qxl.phone.currencyconverter.ui.main.MainActivity) {
                    ((com.qxl.phone.currencyconverter.ui.main.MainActivity) getActivity())
                        .showConversionFragment(favorite.getCurrencyCode());
                }
            }

            @Override
            public void onRemoveFavorite(FavoriteCurrency favorite) {
                showRemoveConfirmDialog(favorite);
                sharedViewModel.notifyFavoriteChanged();
            }
        });

        sharedViewModel.getFavoriteChanged().observe(getViewLifecycleOwner(), changed -> {
            if (Boolean.TRUE.equals(changed)) {
                viewModel.loadFavorites();
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        viewModel.loadFavorites();
    }

    private void updateUI(java.util.List<FavoriteCurrency> favorites) {
        if (favorites == null || favorites.isEmpty()) {
            recyclerView.setVisibility(View.GONE);
            emptyView.setVisibility(View.VISIBLE);
            favoriteCount.setText("0");
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            emptyView.setVisibility(View.GONE);
            favoriteCount.setText(String.valueOf(favorites.size()));
        }
    }

    private void showRemoveConfirmDialog(FavoriteCurrency favorite) {
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle("Remove Favorite")
                .setMessage("Are you sure you want to remove "+ favorite.getCurrencyName()+ " from your collection? " )
                .setPositiveButton("Yes", (dialog, which) -> {
                    viewModel.removeFromFavorites(favorite.getCurrencyCode());
                })
                .setNegativeButton("No", null)
                .show();
    }
}
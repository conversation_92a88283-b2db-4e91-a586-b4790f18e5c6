package com.qxl.phone.currencyconverter.ui.list;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.ui.list.adapter.CurrencyAdapter;
import com.qxl.phone.currencyconverter.model.Currency;
import com.qxl.phone.currencyconverter.util.NetworkUtils;
import com.google.android.material.textfield.TextInputEditText;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import java.util.ArrayList;
import com.qxl.phone.currencyconverter.ui.main.AppSharedViewModel;

public class CurrencyListFragment extends Fragment {
    private CurrencyListViewModel viewModel;
    private CurrencyAdapter adapter;
    private RecyclerView recyclerView;
    private SwipeRefreshLayout swipeRefreshLayout;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_currency_list, container, false);
        recyclerView = view.findViewById(R.id.currency_list);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh);
        adapter = new CurrencyAdapter(requireContext(), new ArrayList<>());
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(adapter);

        viewModel = new ViewModelProvider(this, ViewModelProvider.AndroidViewModelFactory.getInstance(requireActivity().getApplication())).get(CurrencyListViewModel.class);

        AppSharedViewModel sharedViewModel = new ViewModelProvider(requireActivity()).get(AppSharedViewModel.class);
        sharedViewModel.getFavoriteChanged().observe(getViewLifecycleOwner(), changed -> {
            if (Boolean.TRUE.equals(changed)) {
                viewModel.refreshFavoriteStatus();
            }
        });

        setupAdapterListeners();

        TextInputEditText searchInput = view.findViewById(R.id.search_input);
        searchInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                viewModel.searchCurrencies(s.toString());
            }
        });

        viewModel.getFilteredCurrencyList().observe(getViewLifecycleOwner(), list -> {
            adapter.updateData(list);
        });

        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), msg -> {
            if (msg != null && !msg.isEmpty()) {
                Toast.makeText(requireContext(), msg, Toast.LENGTH_LONG).show();
                swipeRefreshLayout.setRefreshing(false);
            }
        });

        swipeRefreshLayout.setOnRefreshListener(() -> {
            if (NetworkUtils.isNetworkAvailable(requireContext())) {
                viewModel.refreshRates();
            } else {
                Toast.makeText(requireContext(), "Please check your network settings!", Toast.LENGTH_LONG).show();
                swipeRefreshLayout.setRefreshing(false);
            }
        });

        viewModel.getRatesLiveData().observe(getViewLifecycleOwner(), rates -> {
            adapter.setRatesMap(rates);

            if (swipeRefreshLayout.isRefreshing()) {
                Toast.makeText(requireContext(), 
                    "Your exchange rate has been refreshed!", 
                    Toast.LENGTH_SHORT).show();
            }
            
            swipeRefreshLayout.setRefreshing(false);
        });

        return view;
    }
    
    private void setupAdapterListeners() {
        if (adapter == null) {
            System.err.println("The adapter is empty and the listener cannot be set！");
            return;
        }
        
        adapter.setOnCurrencyClickListener(currency -> {
        });
        
        adapter.setOnFavoriteClickListener((currency, isFavorite) -> {
            if (currency == null) {
                Toast.makeText(requireContext(), "Invalid currency data", Toast.LENGTH_SHORT).show();
                return;
            }

            if (isFavorite) {
                viewModel.addToFavorites(currency);
                Toast.makeText(requireContext(), "Added to favorites", Toast.LENGTH_SHORT).show();
            } else {
                viewModel.removeFromFavorites(currency);
                Toast.makeText(requireContext(), "Removed from Collection", Toast.LENGTH_SHORT).show();
            }

            viewModel.refreshFavoriteStatus();
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        viewModel.refreshFavoriteStatus();
    }
}
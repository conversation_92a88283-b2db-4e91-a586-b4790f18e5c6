package com.qxl.phone.currencyconverter.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "exchange_rates")
public class ExchangeRate {
    @PrimaryKey(autoGenerate = true)
    public int id;
    public String fromCurrency;
    public String toCurrency;
    public double rate;
    public String date;
    public long timestamp;
    
    public ExchangeRate(String fromCurrency, String toCurrency, double rate, String date) {
        this.fromCurrency = fromCurrency;
        this.toCurrency = toCurrency;
        this.rate = rate;
        this.date = date;
        this.timestamp = System.currentTimeMillis();
    }
    
    public double convert(double amount) {
        return amount * rate;
    }
    
    public String getFormattedRate() {
        return String.format("1 %s = %.4f %s", fromCurrency, rate, toCurrency);
    }
    
    public String getFormattedResult(double amount) {
        return String.format("%.2f %s", convert(amount), toCurrency);
    }

    public double getRate() {
        return rate;
    }
    
    public String getFromCurrency() {
        return fromCurrency;
    }
    
    public String getToCurrency() {
        return toCurrency;
    }
    
    public String getDate() {
        return date;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
}
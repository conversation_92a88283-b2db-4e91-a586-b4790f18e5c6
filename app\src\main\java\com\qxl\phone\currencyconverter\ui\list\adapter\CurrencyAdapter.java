package com.qxl.phone.currencyconverter.ui.list.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.google.android.material.button.MaterialButton;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ArrayList;

public class CurrencyAdapter extends RecyclerView.Adapter<CurrencyAdapter.ViewHolder> {
    private List<Currency> currencyList;
    private Map<String, Double> ratesMap;
    private final LayoutInflater inflater;
    private OnCurrencyClickListener listener;
    private OnFavoriteClickListener favoriteListener;

    public interface OnCurrencyClickListener {
        void onCurrencyClick(Currency currency);
    }
    
    public interface OnFavoriteClickListener {
        void onFavoriteClick(Currency currency, boolean isFavorite);
    }
    
    public CurrencyAdapter(Context context, List<Currency> list) {
        this.currencyList = list != null ? list : new ArrayList<>();
        this.inflater = LayoutInflater.from(context);
    }
    
    public void setOnCurrencyClickListener(OnCurrencyClickListener listener) {
        this.listener = listener;
    }
    
    public void setOnFavoriteClickListener(OnFavoriteClickListener listener) {
        this.favoriteListener = listener;
    }

    public void updateData(List<Currency> newList) {
        this.currencyList = newList != null ? newList : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void setRatesMap(Map<String, Double> rates) {
        this.ratesMap = rates;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = inflater.inflate(R.layout.item_currency, parent, false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Currency c = currencyList.get(position);
        if (c == null) {
            return;
        }
        
        holder.textCode.setText(c.code != null ? c.code : "");
        holder.textName.setText(c.englishName != null ? c.englishName : "");
        holder.textDescription.setText(getCurrencyDescription(c.code));

        if (ratesMap != null && ratesMap.containsKey(c.code) && ratesMap.get(c.code) != null) {
            double usdToTarget = ratesMap.get(c.code);
            if ("USD".equals(c.code)) {
                holder.textRate.setText("1 USD = 1.0000 USD");
            } else if (usdToTarget != 0) {
                double targetToUsd = 1.0 / usdToTarget;
                holder.textRate.setText(String.format(Locale.getDefault(), "1 %s = %.4f USD", c.code, targetToUsd));
            } else {
                holder.textRate.setText("Loading...");
            }
        } else {
            holder.textRate.setText("Loading...");
        }
        holder.favoriteButton.setIconResource(c.favorite ? R.drawable.ic_star_filled : R.drawable.ic_star_border);

        holder.favoriteButton.setOnClickListener(v -> {
            if (favoriteListener != null && c != null) {
                favoriteListener.onFavoriteClick(c, !c.favorite);
            }
        });
    }

    @Override
    public int getItemCount() {
        return currencyList == null ? 0 : currencyList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textCode, textName, textDescription, textRate;
        MaterialButton favoriteButton;

        ViewHolder(View itemView) {
            super(itemView);
            textCode = itemView.findViewById(R.id.text_code);
            textName = itemView.findViewById(R.id.text_name);
            textDescription = itemView.findViewById(R.id.text_description);
            textRate = itemView.findViewById(R.id.text_rate);
            favoriteButton = itemView.findViewById(R.id.btn_favorite);
        }
    }

    private String getCurrencyDescription(String code) {
        switch (code) {
            case "USD": return "US Dollar";
            case "EUR": return "Euro";
            case "CNY": return "Chinese Yuan";
            case "JPY": return "Japanese Yen";
            case "GBP": return "British Pound";
            case "CAD": return "Canadian Dollar";
            case "AUD": return "Australian Dollar";
            case "CHF": return "Swiss Franc";
            case "HKD": return "Hong Kong Dollar";
            case "KRW": return "South Korean Won";
            default: return code;
        }
    }
}
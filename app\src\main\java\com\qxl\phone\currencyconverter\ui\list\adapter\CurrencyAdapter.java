package com.qxl.phone.currencyconverter.ui.list.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.google.android.material.button.MaterialButton;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.Currency;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ArrayList;

public class CurrencyAdapter extends RecyclerView.Adapter<CurrencyAdapter.ViewHolder> {
    private List<Currency> currencyList;
    private Map<String, Double> ratesMap;
    private final LayoutInflater inflater;
    private OnCurrencyClickListener listener;
    private OnFavoriteClickListener favoriteListener;

    public interface OnCurrencyClickListener {
        void onCurrencyClick(Currency currency);
    }
    
    public interface OnFavoriteClickListener {
        void onFavoriteClick(Currency currency, boolean isFavorite);
    }
    
    public CurrencyAdapter(Context context, List<Currency> list) {
        this.currencyList = list != null ? list : new ArrayList<>();
        this.inflater = LayoutInflater.from(context);
    }
    
    public void setOnCurrencyClickListener(OnCurrencyClickListener listener) {
        this.listener = listener;
    }
    
    public void setOnFavoriteClickListener(OnFavoriteClickListener listener) {
        this.favoriteListener = listener;
    }

    public void updateData(List<Currency> newList) {
        this.currencyList = newList != null ? newList : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void setRatesMap(Map<String, Double> rates) {
        this.ratesMap = rates;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = inflater.inflate(R.layout.item_currency, parent, false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Currency c = currencyList.get(position);
        if (c == null) {
            return;
        }
        
        holder.textCode.setText(c.code != null ? c.code : "");
        holder.textName.setText(c.englishName != null ? c.englishName : "");
        holder.textDescription.setText(getCurrencyDescription(c.code));

        if (ratesMap != null && ratesMap.containsKey(c.code) && ratesMap.get(c.code) != null) {
            double usdToTarget = ratesMap.get(c.code);
            if ("USD".equals(c.code)) {
                holder.textRate.setText(context.getString(R.string.exchange_rate_usd_format));
            } else if (usdToTarget != 0) {
                double targetToUsd = 1.0 / usdToTarget;
                holder.textRate.setText(String.format(Locale.getDefault(),
                    context.getString(R.string.exchange_rate_to_usd_format), c.code, targetToUsd));
            } else {
                holder.textRate.setText(context.getString(R.string.loading));
            }
        } else {
            holder.textRate.setText(context.getString(R.string.loading));
        }
        holder.favoriteButton.setIconResource(c.favorite ? R.drawable.ic_star_filled : R.drawable.ic_star_border);

        holder.favoriteButton.setOnClickListener(v -> {
            if (favoriteListener != null && c != null) {
                favoriteListener.onFavoriteClick(c, !c.favorite);
            }
        });
    }

    @Override
    public int getItemCount() {
        return currencyList == null ? 0 : currencyList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textCode, textName, textDescription, textRate;
        MaterialButton favoriteButton;

        ViewHolder(View itemView) {
            super(itemView);
            textCode = itemView.findViewById(R.id.text_code);
            textName = itemView.findViewById(R.id.text_name);
            textDescription = itemView.findViewById(R.id.text_description);
            textRate = itemView.findViewById(R.id.text_rate);
            favoriteButton = itemView.findViewById(R.id.btn_favorite);
        }
    }

    private String getCurrencyDescription(String code) {
        switch (code) {
            case "USD": return context.getString(R.string.currency_usd);
            case "EUR": return context.getString(R.string.currency_eur);
            case "CNY": return context.getString(R.string.currency_cny);
            case "JPY": return context.getString(R.string.currency_jpy);
            case "GBP": return context.getString(R.string.currency_gbp);
            case "CAD": return context.getString(R.string.currency_cad);
            case "AUD": return context.getString(R.string.currency_aud);
            case "CHF": return context.getString(R.string.currency_chf);
            case "HKD": return context.getString(R.string.currency_hkd);
            case "KRW": return context.getString(R.string.currency_krw);
            case "SGD": return context.getString(R.string.currency_sgd);
            case "NZD": return context.getString(R.string.currency_nzd);
            case "SEK": return context.getString(R.string.currency_sek);
            case "NOK": return context.getString(R.string.currency_nok);
            case "DKK": return context.getString(R.string.currency_dkk);
            case "PLN": return context.getString(R.string.currency_pln);
            case "CZK": return context.getString(R.string.currency_czk);
            case "HUF": return context.getString(R.string.currency_huf);
            case "RUB": return context.getString(R.string.currency_rub);
            case "TRY": return context.getString(R.string.currency_try);
            case "BRL": return context.getString(R.string.currency_brl);
            case "MXN": return context.getString(R.string.currency_mxn);
            case "INR": return context.getString(R.string.currency_inr);
            case "THB": return context.getString(R.string.currency_thb);
            case "MYR": return context.getString(R.string.currency_myr);
            case "IDR": return context.getString(R.string.currency_idr);
            case "PHP": return context.getString(R.string.currency_php);
            case "VND": return context.getString(R.string.currency_vnd);
            case "TWD": return context.getString(R.string.currency_twd);
            default: return code;
        }
    }
}
package com.qxl.phone.currencyconverter.data.enum2;



import com.qxl.phone.currencyconverter.R;

import java.util.Locale;

public enum Language {
    DEFAULT(new Locale(""), R.string.string_language_default),  // 系统默认
    ENGLISH(new Locale("en"), R.string.string_language_english),          // 英语
    SPANISH(new Locale("es"), R.string.string_language_spanish),          // 西班牙语
    FRENCH(new Locale("fr"), R.string.string_language_french),          // 法语
    JAPANESE(new Locale("ja"), R.string.string_language_japanese),           // 日语
    KOREAN(new Locale("ko"), R.string.string_language_korean),             // 韩语
    PORTUGUESE(new Locale("pt"), R.string.string_language_portuguese),     // 葡萄牙语
    RUSSIAN(new Locale("ru"), R.string.string_language_russian);          // 俄语

    private final Locale locale;
    private final int stringRes;

    Language(Locale locale, int stringRes) {
        this.locale = locale;
        this.stringRes = stringRes;
    }

    public Locale getLocale() {
        return locale;
    }

    public int getStringRes() {
        return stringRes;
    }

}

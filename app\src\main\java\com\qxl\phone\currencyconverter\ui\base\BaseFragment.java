package com.qxl.phone.currencyconverter.ui.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewbinding.ViewBinding;

/**
 * 基础 Fragment 类，实现 ViewBinding
 * @param <VB> ViewBinding 类型
 */
public abstract class BaseFragment<VB extends ViewBinding> extends Fragment {
    protected VB viewBinding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        viewBinding = initBinding(inflater, container);
        return viewBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData(getArguments());
        initView();
        initEvent();
        loadData();
    }

    @NonNull
    public abstract VB initBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container);

    public void initData(@Nullable Bundle arguments) {
    }

    public void initView() {
    }

    public void initEvent() {
    }

    public void loadData() {
    }
} 
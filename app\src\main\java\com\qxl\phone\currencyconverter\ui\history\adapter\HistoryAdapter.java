package com.qxl.phone.currencyconverter.ui.history.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.ConversionHistory;
import java.util.List;
import java.util.Locale;

public class HistoryAdapter extends RecyclerView.Adapter<HistoryAdapter.ViewHolder> {
    private List<ConversionHistory> data;
    private final LayoutInflater inflater;

    public HistoryAdapter(Context context, List<ConversionHistory> list) {
        this.data = list;
        this.inflater = LayoutInflater.from(context);
    }

    public void updateData(List<ConversionHistory> newData) {
        this.data = newData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = inflater.inflate(R.layout.item_history, parent, false);
        return new ViewHolder(v);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ConversionHistory h = data.get(position);
        holder.textFrom.setText(String.format(Locale.getDefault(), "%.2f %s", h.fromAmount, h.fromCurrency));
        holder.textTo.setText(String.format(Locale.getDefault(), "%.2f %s", h.toAmount, h.toCurrency));
        holder.textTime.setText(h.getFormattedTime());
    }

    @Override
    public int getItemCount() {
        return data == null ? 0 : data.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textFrom, textTo, textTime;

        ViewHolder(View itemView) {
            super(itemView);
            textFrom = itemView.findViewById(R.id.text_from);
            textTo = itemView.findViewById(R.id.text_to);
            textTime = itemView.findViewById(R.id.text_time);
        }
    }
}
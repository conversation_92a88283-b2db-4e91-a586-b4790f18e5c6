<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true"
    android:paddingTop="@dimen/sw_dp_30">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题分区 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
                android:orientation="vertical"
            android:paddingHorizontal="@dimen/sw_dp_24"
            android:paddingTop="@dimen/sw_dp_12"
            android:paddingBottom="@dimen/sw_dp_12">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                android:gravity="center_vertical">

                    <ImageView
                    android:layout_width="@dimen/sw_dp_28"
                    android:layout_height="@dimen/sw_dp_28"
                        android:src="@drawable/ic_settings"
                        android:tint="@color/primary"
                        android:layout_marginEnd="@dimen/sw_dp_12"
                        tools:ignore="UseAppTint" />

                    <TextView
                    android:layout_width="@dimen/sw_dp_0"
                        android:layout_height="wrap_content"
                    android:layout_weight="1"
                        android:text="@string/settings"
                    android:textSize="@dimen/sw_dp_22"
                        android:textStyle="bold"
                        android:textColor="@color/on_surface" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/app_preferences"
                    android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/on_surface_variant"
                android:layout_marginTop="@dimen/sw_dp_4" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/sw_dp_1"
                android:background="@color/outline_variant"
                android:layout_marginTop="@dimen/sw_dp_12"/>
            </LinearLayout>

        <!-- 主题设置卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/sw_dp_16"
            app:cardCornerRadius="@dimen/sw_dp_12"
            app:cardElevation="@dimen/sw_dp_2"
            app:cardBackgroundColor="@color/surface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/sw_dp_16">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/appearance"
                    android:textSize="@dimen/sw_dp_16"
                    android:textStyle="bold"
                    android:textColor="@color/on_surface"
                    android:layout_marginBottom="@dimen/sw_dp_8" />

                <!-- 语言选择 -->
                <LinearLayout
                    android:id="@+id/layout_language"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingVertical="@dimen/sw_dp_8"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackground">

                    <ImageView
                        android:layout_width="@dimen/sw_dp_20"
                        android:layout_height="@dimen/sw_dp_20"
                        android:src="@drawable/ic_language"
                        android:layout_marginEnd="@dimen/sw_dp_12"
                        app:tint="@color/primary" />

                    <TextView
                        android:layout_width="@dimen/sw_dp_0"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/language"
                        android:textSize="@dimen/sw_dp_16"
                        android:textColor="@color/on_surface" />

                    <ImageView
                        android:layout_width="@dimen/sw_dp_16"
                        android:layout_height="@dimen/sw_dp_16"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/on_surface_variant" />

                </LinearLayout>

                <!-- 分隔线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/sw_dp_1"
                    android:background="@color/outline_variant"
                    android:layout_marginVertical="@dimen/sw_dp_8" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingVertical="@dimen/sw_dp_8">

                    <ImageView
                        android:layout_width="@dimen/sw_dp_20"
                        android:layout_height="@dimen/sw_dp_20"
                        android:src="@drawable/ic_dark_mode"
                        android:layout_marginEnd="@dimen/sw_dp_12"
                        app:tint="@color/primary" />

                    <TextView
                        android:layout_width="@dimen/sw_dp_0"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/dark_mode"
                        android:textSize="@dimen/sw_dp_16"
                        android:textColor="@color/on_surface" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 数据管理卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_clear_cache"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/sw_dp_16"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground"
            app:cardCornerRadius="@dimen/sw_dp_12"
            app:cardElevation="@dimen/sw_dp_2"
            app:cardBackgroundColor="@color/surface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/sw_dp_16">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/sw_dp_12">

                    <ImageView
                        android:layout_width="@dimen/sw_dp_20"
                        android:layout_height="@dimen/sw_dp_20"
                        android:src="@drawable/ic_clear_cache"
                        android:layout_marginEnd="@dimen/sw_dp_12"
                        app:tint="@color/primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/clear_cache"
                    android:textSize="@dimen/sw_dp_16"
                    android:textStyle="bold"
                        android:textColor="@color/on_surface" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/clear_cache_hint"
                    android:textSize="@dimen/sw_dp_13"
                    android:textColor="@color/secondary_text"
                    android:layout_marginTop="@dimen/sw_dp_8" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/sw_dp_16"
            app:cardCornerRadius="@dimen/sw_dp_12"
            app:cardElevation="@dimen/sw_dp_2"
            app:cardBackgroundColor="@color/surface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/sw_dp_16">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/sw_dp_12"
                    android:text="@string/about"
                    android:textColor="@color/on_surface"
                    android:textSize="@dimen/sw_dp_16"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/sw_dp_4"
                    android:text="@string/app_version"
                    android:textColor="@color/on_surface"
                    android:textSize="@dimen/sw_dp_14" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_privacy_policy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/sw_dp_16"
            app:cardCornerRadius="@dimen/sw_dp_12"
            app:cardElevation="@dimen/sw_dp_2"
            app:cardBackgroundColor="@color/surface">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/sw_dp_16">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/privacy_policy"
                        android:textColor="@color/on_surface"
                        android:textSize="@dimen/sw_dp_16"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- 占位View将ImageView推到最右 -->
                <View
                    android:layout_width="@dimen/sw_dp_0"
                    android:layout_height="@dimen/sw_dp_1"
                    android:layout_weight="1" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    android:src="@drawable/ic_arrow_right"
                    android:layout_marginEnd="@dimen/sw_dp_12" />
            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
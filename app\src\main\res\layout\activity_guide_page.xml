<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingConstraints"
    android:fitsSystemWindows="true">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/guidePage_viewPager2"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_dp_0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <View
        android:id="@+id/guidePage_view"
        android:layout_width="match_parent"
        android:layout_height="165dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <View
        android:id="@+id/guidePage_viewIndicator"
        android:layout_width="@dimen/sw_dp_0"
        android:layout_height="@dimen/sw_dp_40"
        app:layout_constraintBottom_toTopOf="@id/guidePage_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.98" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/guidePage_ivNav"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/sw_dp_6"
        android:adjustViewBounds="true"
        android:src="@drawable/guide_indicator_img_1"
        app:layout_constraintBottom_toBottomOf="@id/guidePage_viewIndicator"
        app:layout_constraintEnd_toEndOf="@id/guidePage_viewIndicator"
        app:layout_constraintStart_toStartOf="@id/guidePage_viewIndicator"
        app:layout_constraintTop_toTopOf="@id/guidePage_viewIndicator" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/guidePage_tvNext"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_marginEnd="@dimen/sw_dp_20"
        android:gravity="center"
        android:paddingHorizontal="15dp"
        android:text="@string/string_next"
        android:textColor="@color/color_1976D3"
        android:textSize="15dp"
        app:layout_constraintBottom_toBottomOf="@id/guidePage_viewIndicator"
        app:layout_constraintEnd_toEndOf="@id/guidePage_viewIndicator"
        app:layout_constraintTop_toTopOf="@id/guidePage_viewIndicator" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/guidePage_tvSkip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/sw_dp_20"
        android:paddingVertical="@dimen/sw_dp_10"
        android:text="@string/string_skip"
        android:textColor="@color/color_1976D3"
        android:textSize="15dp"
        app:layout_constraintBottom_toBottomOf="@id/guidePage_viewIndicator"
        app:layout_constraintStart_toStartOf="@id/guidePage_viewIndicator"
        app:layout_constraintTop_toTopOf="@id/guidePage_viewIndicator" />

</androidx.constraintlayout.widget.ConstraintLayout>
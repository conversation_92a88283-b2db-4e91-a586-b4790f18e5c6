<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外发光层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="@dimen/sw_dp_200"
                android:centerX="0.5"
                android:centerY="0.5"
                android:startColor="#40FFFFFF"
                android:endColor="#00FFFFFF" />
            <corners android:radius="@dimen/sw_dp_20" />
            <padding
                android:bottom="@dimen/sw_dp_8"
                android:left="@dimen/sw_dp_8"
                android:right="@dimen/sw_dp_8"
                android:top="@dimen/sw_dp_8" />
        </shape>
    </item>
    
    <!-- 主背景层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/primary"
                android:endColor="@color/tertiary"
                android:centerColor="@color/secondary" />
            <corners android:radius="@dimen/sw_dp_20" />
        </shape>
    </item>
    
    <!-- 内发光层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="@dimen/sw_dp_100"
                android:centerX="0.3"
                android:centerY="0.3"
                android:startColor="#30FFFFFF"
                android:endColor="#00FFFFFF" />
            <corners android:radius="@dimen/sw_dp_20" />
        </shape>
    </item>
</layer-list> 
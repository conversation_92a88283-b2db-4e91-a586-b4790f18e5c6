<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 阴影层 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#20000000" />
            <corners android:radius="@dimen/sw_dp_20" />
            <padding
                android:bottom="@dimen/sw_dp_2"
                android:left="@dimen/sw_dp_2"
                android:right="@dimen/sw_dp_2"
                android:top="@dimen/sw_dp_2" />
        </shape>
    </item>
    
    <!-- 主背景层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/primary"
                android:endColor="@color/tertiary"
                android:centerColor="@color/secondary" />
            <corners android:radius="@dimen/sw_dp_20" />
        </shape>
    </item>
</layer-list> 
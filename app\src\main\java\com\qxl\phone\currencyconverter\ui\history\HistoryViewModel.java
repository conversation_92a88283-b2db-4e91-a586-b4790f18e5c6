package com.qxl.phone.currencyconverter.ui.history;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.qxl.phone.currencyconverter.data.AppDatabase;
import com.qxl.phone.currencyconverter.model.ConversionHistory;
import android.os.AsyncTask;
import java.util.ArrayList;
import java.util.List;
import com.qxl.phone.currencyconverter.util.AppExecutors;

public class HistoryViewModel extends AndroidViewModel {
    private final LiveData<List<ConversionHistory>> historyListLiveData;

    public HistoryViewModel(@NonNull Application application) {
        super(application);
        historyListLiveData = AppDatabase.getInstance(application).historyDao().getAllLive();
    }

    public LiveData<List<ConversionHistory>> getHistoryListLiveData() {
        return historyListLiveData;
    }

    public void clearHistory() {
        AppExecutors.DB_EXECUTOR.execute(() -> {
            AppDatabase db = AppDatabase.getInstance(getApplication());
            db.historyDao().clearAll();
        });
    }
}
package com.qxl.phone.currencyconverter.ui.main;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.ui.list.CurrencyListFragment;
import com.qxl.phone.currencyconverter.ui.favorite.FavoriteFragment;
import com.qxl.phone.currencyconverter.ui.history.HistoryFragment;
import com.qxl.phone.currencyconverter.ui.settings.SettingsFragment;
import com.qxl.phone.currencyconverter.data.PreferencesHelper;
import android.os.Build;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.graphics.Color;
import androidx.viewpager2.widget.ViewPager2;

public class MainActivity extends AppCompatActivity {
    private BottomNavigationView bottomNavigationView;
    private ViewPager2 viewPager;
    private boolean userAction = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        }

        bottomNavigationView = findViewById(R.id.bottom_navigation);
        viewPager = findViewById(R.id.view_pager);
        viewPager.setAdapter(new MainPagerAdapter(this));
        viewPager.setOffscreenPageLimit(4);

        bottomNavigationView.setOnItemSelectedListener(item -> {
            userAction = false;
            int id = item.getItemId();
            if (id == R.id.nav_conversion) {
                viewPager.setCurrentItem(0, false);
            } else if (id == R.id.nav_currencies) {
                viewPager.setCurrentItem(1, false);
            } else if (id == R.id.nav_favorites) {
                viewPager.setCurrentItem(2, false);
            } else if (id == R.id.nav_history) {
                viewPager.setCurrentItem(3, false);
            } else if (id == R.id.nav_settings) {
                viewPager.setCurrentItem(4, false);
            }
            userAction = true;
            return true;
        });

        viewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                if (userAction) {
                    if (position == 0) {
                        bottomNavigationView.setSelectedItemId(R.id.nav_conversion);
                    } else if (position == 1) {
                        bottomNavigationView.setSelectedItemId(R.id.nav_currencies);
                    } else if (position == 2) {
                        bottomNavigationView.setSelectedItemId(R.id.nav_favorites);
                    } else if (position == 3) {
                        bottomNavigationView.setSelectedItemId(R.id.nav_history);
                    } else if (position == 4) {
                        bottomNavigationView.setSelectedItemId(R.id.nav_settings);
                    }
                }
            }
        });

        if (savedInstanceState == null) {
            viewPager.setCurrentItem(0, false);
        }
    }

    public void restartApp() {
        Intent intent = getIntent();
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    public void showConversionFragment(String toCurrencyCode) {
        viewPager.setCurrentItem(0, false);
    }
}
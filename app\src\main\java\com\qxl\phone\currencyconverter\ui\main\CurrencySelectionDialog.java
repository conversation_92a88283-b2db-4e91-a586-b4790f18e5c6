package com.qxl.phone.currencyconverter.ui.main;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.Currency;
import java.util.ArrayList;
import java.util.List;


public class CurrencySelectionDialog extends DialogFragment {
    private List<Currency> currencyList;
    private OnCurrencySelectedListener listener;
    private String title;

    public interface OnCurrencySelectedListener {
        void onCurrencySelected(Currency currency);
    }

    public static CurrencySelectionDialog newInstance(List<Currency> currencies, String title) {
        CurrencySelectionDialog dialog = new CurrencySelectionDialog();
        dialog.currencyList = new ArrayList<>(currencies);
        dialog.title = title;
        return dialog;
    }

    public void setOnCurrencySelectedListener(OnCurrencySelectedListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext());
        builder.setTitle(title);

        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_currency_selection, null);
        EditText searchEditText = view.findViewById(R.id.search_edit_text);
        ListView listView = view.findViewById(R.id.currency_list);

        CurrencyListAdapter adapter = new CurrencyListAdapter(getContext(), currencyList);
        listView.setAdapter(adapter);

        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                adapter.filter(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        listView.setOnItemClickListener((parent, view1, position, id) -> {
            Currency selectedCurrency = adapter.getItem(position);
            if (listener != null) {
                listener.onCurrencySelected(selectedCurrency);
            }
            dismiss();
        });

        builder.setView(view);
        return builder.create();
    }

    private static class CurrencyListAdapter extends ArrayAdapter<Currency> {
        private List<Currency> originalList;
        private List<Currency> filteredList;

        public CurrencyListAdapter(Context context, List<Currency> currencies) {
            super(context, android.R.layout.simple_list_item_1, currencies);
            this.originalList = new ArrayList<>(currencies);
            this.filteredList = new ArrayList<>(currencies);
        }

        public void filter(String query) {
            filteredList.clear();
            if (query == null || query.isEmpty()) {
                filteredList.addAll(originalList);
            } else {
                String lowerQuery = query.toLowerCase();
                for (Currency currency : originalList) {
                    if (currency.code.toLowerCase().contains(lowerQuery) ||
                            currency.englishName.toLowerCase().contains(lowerQuery) ||
                            currency.englishName.toLowerCase().contains(lowerQuery)) {
                        filteredList.add(currency);
                    }
                }
            }
            notifyDataSetChanged();
        }

        @Override
        public int getCount() {
            return filteredList.size();
        }

        @Override
        public Currency getItem(int position) {
            return filteredList.get(position);
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            if (convertView == null) {
                convertView = LayoutInflater.from(getContext()).inflate(android.R.layout.simple_list_item_2, parent, false);
            }

            Currency currency = getItem(position);
            TextView text1 = convertView.findViewById(android.R.id.text1);
            TextView text2 = convertView.findViewById(android.R.id.text2);

            text1.setText(currency.code + " - " + currency.englishName);
            text2.setText(currency.englishName);

            return convertView;
        }
    }
}
package com.qxl.phone.currencyconverter.ui.main;

import android.app.Application;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.qxl.phone.currencyconverter.model.ConversionHistory;
import com.qxl.phone.currencyconverter.model.Currency;
import com.qxl.phone.currencyconverter.model.ExchangeRate;
import com.qxl.phone.currencyconverter.util.ExchangeRateApi;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import com.qxl.phone.currencyconverter.data.AppDatabase;
import android.os.AsyncTask;

public class MainViewModel extends AndroidViewModel {
    private final MutableLiveData<ExchangeRate> liveRate = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final ExchangeRateApi api;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private final List<Currency> currencyList = new ArrayList<>();
    private static final Map<String, String> CURRENCY_NAME_MAP = new HashMap<>();
    
    static {
        CURRENCY_NAME_MAP.put("USD", "US Dollar");
        CURRENCY_NAME_MAP.put("CNY", "Chinese Yuan");
        CURRENCY_NAME_MAP.put("EUR", "Euro");
        CURRENCY_NAME_MAP.put("JPY", "Japanese Yen");
        CURRENCY_NAME_MAP.put("GBP", "British Pound");
        CURRENCY_NAME_MAP.put("CAD", "Canadian Dollar");
        CURRENCY_NAME_MAP.put("AUD", "Australian Dollar");
        CURRENCY_NAME_MAP.put("CHF", "Swiss Franc");
        CURRENCY_NAME_MAP.put("HKD", "Hong Kong Dollar");
        CURRENCY_NAME_MAP.put("KRW", "South Korean Won");
        CURRENCY_NAME_MAP.put("SGD", "Singapore Dollar");
        CURRENCY_NAME_MAP.put("NZD", "New Zealand Dollar");
        CURRENCY_NAME_MAP.put("SEK", "Swedish Krona");
        CURRENCY_NAME_MAP.put("NOK", "Norwegian Krone");
        CURRENCY_NAME_MAP.put("DKK", "Danish Krona");
        CURRENCY_NAME_MAP.put("PLN", "Polish Zloty");
        CURRENCY_NAME_MAP.put("CZK", "Czech Koruna");
        CURRENCY_NAME_MAP.put("HUF", "Hungarian Forint");
        CURRENCY_NAME_MAP.put("RUB", "Russian Ruble");
        CURRENCY_NAME_MAP.put("TRY", "Turkish Lira");
        CURRENCY_NAME_MAP.put("BRL", "Brazilian Real");
        CURRENCY_NAME_MAP.put("MXN", "Mexican Peso");
        CURRENCY_NAME_MAP.put("INR", "Indian Rupee");
        CURRENCY_NAME_MAP.put("THB", "Thai Baht");
        CURRENCY_NAME_MAP.put("MYR", "Malaysian Ringgit");
        CURRENCY_NAME_MAP.put("IDR", "Indonesian Rupiah");
        CURRENCY_NAME_MAP.put("PHP", "Philippine Peso");
        CURRENCY_NAME_MAP.put("VND", "Vietnamese Dong");
        CURRENCY_NAME_MAP.put("TWD", "New Taiwan Dollar");
    }
    
    public MainViewModel(Application application) {
        super(application);
        api = new ExchangeRateApi(application);
        initializeCurrencies();

        scheduler.scheduleAtFixedRate(this::updateRates, 0, 15, TimeUnit.MINUTES);
    }
    
    private void initializeCurrencies() {
        String[] mainCurrencies = {
            "USD", "CNY", "EUR", "JPY", "GBP", "CAD", "AUD", "CHF", "HKD", "KRW",
            "SGD", "NZD", "SEK", "NOK", "DKK", "PLN", "CZK", "HUF", "RUB", "TRY",
            "BRL", "MXN", "INR", "THB", "MYR", "IDR", "PHP", "VND", "TWD"
        };
        
        for (String code : mainCurrencies) {
            String name = CURRENCY_NAME_MAP.getOrDefault(code, code);
            currencyList.add(new Currency(code, name, false));
        }
    }
    
    public List<Currency> getCurrencyList() {
        return currencyList;
    }
    
    public List<String> getCurrencyCodes(List<Currency> currencies) {
        List<String> codes = new ArrayList<>();
        for (Currency currency : currencies) {
            codes.add(currency.code);
        }
        return codes;
    }
    
    public LiveData<ExchangeRate> getLiveRate() {
        return liveRate;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public void fetchRate(String fromCurrency, String toCurrency) {
        if (fromCurrency.equals(toCurrency)) {
            errorMessage.setValue("It cannot be converted to the same currency");
            return;
        }
        
        isLoading.setValue(true);
        errorMessage.setValue(null);
        
        api.getExchangeRate(fromCurrency, toCurrency, new ExchangeRateApi.RateCallback() {
            @Override
            public void onSuccess(ExchangeRate rate) {
                liveRate.postValue(rate);
                isLoading.postValue(false);
            }
            
            @Override
            public void onError(String error) {
                errorMessage.postValue(error);
                isLoading.postValue(false);
            }
        });
    }
    
    public void convertAmount(String fromCurrency, String toCurrency, double amount) {
        if (amount <= 0) {
            errorMessage.setValue("Please enter a valid amount");
            return;
        }
        
        fetchRate(fromCurrency, toCurrency);
    }

    public void saveConversionHistory(String fromCurrency, String toCurrency,
                                      double fromAmount, double toAmount, double rate) {
        ConversionHistory history = new ConversionHistory(fromCurrency, toCurrency,
                fromAmount, toAmount, rate);
        AsyncTask.execute(() -> {
            AppDatabase db = AppDatabase.getInstance(getApplication());
            db.historyDao().insert(history);
            System.out.println("[DEBUG] Inserted history: " + history.getFormattedConversion() + ", time: " + history.getFormattedTime());
        });
    }
    
    private void updateRates() {
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        scheduler.shutdown();
    }
}
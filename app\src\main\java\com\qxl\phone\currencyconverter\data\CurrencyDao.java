package com.qxl.phone.currencyconverter.data;

import androidx.room.*;
import com.qxl.phone.currencyconverter.model.Currency;
import java.util.List;

@Dao
public interface CurrencyDao {
    @Query("SELECT * FROM currencies")
    List<Currency> getAll();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<Currency> currencies);

    @Update
    void update(Currency currency);

    @Query("SELECT * FROM currencies WHERE favorite = 1")
    List<Currency> getFavorites();

    @Query("DELETE FROM currencies")
    void clearAll();
}
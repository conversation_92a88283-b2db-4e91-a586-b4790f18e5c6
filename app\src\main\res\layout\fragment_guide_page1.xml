<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/landingPage1_img"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_dp_450"
        android:scaleType="centerCrop"
        android:src="@drawable/img_guide1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/landingPage2_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/sw_dp_10"
        android:layout_marginTop="@dimen/sw_dp_20"
        android:gravity="center"
        android:text="@string/string_landingPage1_title"
        android:textColor="@color/color_333333"
        android:textSize="@dimen/sw_dp_30"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/landingPage1_img" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/sw_dp_5"
        android:gravity="center"
        android:text="@string/string_landingPage1_tips"
        android:textColor="@color/color_999999"
        android:textSize="@dimen/sw_dp_14"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/landingPage2_tips" />


</androidx.constraintlayout.widget.ConstraintLayout>
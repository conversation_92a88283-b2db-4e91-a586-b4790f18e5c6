<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="@dimen/sw_dp_0">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_dp_260"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="@color/primary"
            app:expandedTitleMarginStart="@dimen/sw_dp_24"
            app:expandedTitleMarginEnd="@dimen/sw_dp_24">

                <LinearLayout
                    android:id="@+id/title_layout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:background="@drawable/gradient_background">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/currency_converter"
                        android:textSize="@dimen/sw_dp_28"
                        android:textStyle="bold"
                        android:textColor="@color/white"
                        android:layout_marginBottom="@dimen/sw_dp_8" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/real_time_rates"
                        android:textSize="@dimen/sw_dp_16"
                        android:textColor="@color/white"
                        android:alpha="0.9" />

                </LinearLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/sw_dp_20">

            <!-- 货币选择区域 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/sw_dp_20"
                app:cardCornerRadius="@dimen/sw_dp_20"
                app:cardElevation="@dimen/sw_dp_8"
                app:cardBackgroundColor="@color/surface"
                app:strokeWidth="@dimen/sw_dp_1"
                app:strokeColor="@color/outline_variant">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/sw_dp_24">

                    <!-- 金额输入 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/sw_dp_16">

                        <ImageView
                            android:layout_width="@dimen/sw_dp_32"
                            android:layout_height="@dimen/sw_dp_32"
                            android:src="@drawable/ic_amount"
                            app:tint="@color/primary"
                            android:layout_marginEnd="@dimen/sw_dp_12" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="@dimen/sw_dp_0"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/amount"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/edit_amount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="numberDecimal" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- 从货币 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/sw_dp_16">

                        <ImageView
                            android:layout_width="@dimen/sw_dp_32"
                            android:layout_height="@dimen/sw_dp_32"
                            android:src="@drawable/ic_currency_from"
                            app:tint="@color/primary"
                            android:layout_marginEnd="@dimen/sw_dp_12" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="@dimen/sw_dp_0"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/from_currency"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <AutoCompleteTextView
                                android:id="@+id/spinner_from"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:focusable="false"
                                android:clickable="true"
                                android:drawableEnd="@drawable/ic_arrow_forward"
                                android:background="@null"
                                android:textColor="@color/primary_text"
                                android:textSize="@dimen/sw_dp_16" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- 交换按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_swap"
                        android:layout_width="@dimen/sw_dp_56"
                        android:layout_height="@dimen/sw_dp_56"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="@dimen/sw_dp_16"
                        app:icon="@drawable/ic_swap"
                        app:iconSize="@dimen/sw_dp_24"
                        app:cornerRadius="@dimen/sw_dp_28"
                        android:backgroundTint="@color/primary"
                        app:iconTint="@color/on_primary"
                        app:iconGravity="textStart"
                        app:iconPadding="@dimen/sw_dp_0"
                        android:gravity="center"
                        android:text=""
                        style="@style/Widget.Material3.Button.IconButton.Filled" />

                    <!-- 到货币 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="@dimen/sw_dp_20">

                        <ImageView
                            android:layout_width="@dimen/sw_dp_32"
                            android:layout_height="@dimen/sw_dp_32"
                            android:src="@drawable/ic_currency_to"
                            app:tint="@color/primary"
                            android:layout_marginEnd="@dimen/sw_dp_12" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="@dimen/sw_dp_0"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/to_currency"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                            <AutoCompleteTextView
                                android:id="@+id/spinner_to"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:focusable="false"
                                android:clickable="true"
                                android:drawableEnd="@drawable/ic_arrow_forward"
                                android:background="@null"
                                android:textColor="@color/primary_text"
                                android:textSize="@dimen/sw_dp_16" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 转换按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_convert"
                android:layout_width="match_parent"
                android:layout_height="@dimen/sw_dp_64"
                android:layout_marginTop="@dimen/sw_dp_20"
                android:text="@string/convert"
                android:textSize="@dimen/sw_dp_18"
                android:textStyle="bold"
                android:gravity="center"
                android:textColor="@color/on_primary"
                android:backgroundTint="@color/primary"
                app:cornerRadius="@dimen/sw_dp_32"
                app:elevation="@dimen/sw_dp_8" />

            <!-- 进度指示器 -->
            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress_indicator"
                android:layout_width="@dimen/sw_dp_48"
                android:layout_height="@dimen/sw_dp_48"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/sw_dp_16"
                android:visibility="gone"
                app:indicatorSize="@dimen/sw_dp_48"
                app:trackThickness="@dimen/sw_dp_4" />

            <!-- 结果显示 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/result_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/sw_dp_20"
                android:visibility="gone"
                app:cardCornerRadius="@dimen/sw_dp_20"
                app:cardElevation="@dimen/sw_dp_8"
                app:cardBackgroundColor="@color/primary_container"
                app:strokeWidth="@dimen/sw_dp_2"
                app:strokeColor="@color/primary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/sw_dp_24"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/result"
                        android:textSize="@dimen/sw_dp_16"
                        android:textColor="@color/on_primary_container"
                        android:layout_marginBottom="@dimen/sw_dp_12" />

                    <TextView
                        android:id="@+id/text_result"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/sw_dp_32"
                        android:textStyle="bold"
                        android:textColor="@color/on_primary_container"
                        android:gravity="center"
                        android:padding="@dimen/sw_dp_20"
                        android:background="@drawable/result_background" />

                    <TextView
                        android:id="@+id/text_rate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/sw_dp_14"
                        android:textColor="@color/on_primary_container"
                        android:gravity="center"
                        android:layout_marginTop="@dimen/sw_dp_12"
                        android:alpha="0.8" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/language_flContainer"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:layout_marginStart="@dimen/sw_dp_12"
        android:layout_marginEnd="@dimen/sw_dp_12"
        android:layout_marginBottom="@dimen/sw_dp_6"
        android:background="@drawable/shape_r8_ffffff">


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/language_tvName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/sw_dp_16"
            android:textColor="@color/color_1c1b20"
            android:textSize="15dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="中文（简体）" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/language_ivSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/sw_dp_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/icon_language_select" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
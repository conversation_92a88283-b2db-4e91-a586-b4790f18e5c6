<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingConstraints"
    android:background="@color/white"
    xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/languageAct_vTitleBar"
        android:paddingTop="44dp"
        android:orientation="horizontal">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/sw_dp_13"
            app:srcCompat="@drawable/alt_arrow_left" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="@dimen/sw_dp_0"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_height="wrap_content"
            android:padding="@dimen/sw_dp_13"
            android:text="@string/string_language"
            android:textColor="@color/color_333333"
            android:textSize="17dp"
            app:layout_constraintTop_toTopOf="parent" />
        
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/languageAct_tvDone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:srcCompat="@drawable/icon_done"
            android:padding="@dimen/sw_dp_13"/>
            
    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/languageAct_rv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_dp_0"
        android:layout_marginTop="@dimen/sw_dp_12"
        android:overScrollMode="never"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/languageAct_vTitleBar" />

</androidx.constraintlayout.widget.ConstraintLayout>
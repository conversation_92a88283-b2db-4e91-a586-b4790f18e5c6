package com.qxl.phone.currencyconverter.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "conversion_history")
public class ConversionHistory {
    @PrimaryKey(autoGenerate = true)
    public int id;
    public String fromCurrency;
    public String toCurrency;
    public double fromAmount;
    public double toAmount;
    public double rate;
    public long timestamp;
    
    public ConversionHistory(String fromCurrency, String toCurrency, double fromAmount, 
                           double toAmount, double rate) {
        this.fromCurrency = fromCurrency;
        this.toCurrency = toCurrency;
        this.fromAmount = fromAmount;
        this.toAmount = toAmount;
        this.rate = rate;
        this.timestamp = System.currentTimeMillis();
    }
    
    public String getFormattedConversion() {
        return String.format("%.2f %s = %.2f %s", fromAmount, fromCurrency, toAmount, toCurrency);
    }
    
    public String getFormattedRate() {
        return String.format("1 %s = %.4f %s", fromCurrency, rate, toCurrency);
    }
    
    public String getFormattedTime() {
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", 
                java.util.Locale.getDefault()).format(new java.util.Date(timestamp));
    }
}
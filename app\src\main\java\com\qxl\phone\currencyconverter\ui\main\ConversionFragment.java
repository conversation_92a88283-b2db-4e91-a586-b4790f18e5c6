package com.qxl.phone.currencyconverter.ui.main;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.appbar.AppBarLayout;
import android.graphics.drawable.GradientDrawable;
import android.graphics.Color;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.Currency;
import com.qxl.phone.currencyconverter.model.ExchangeRate;
import java.util.List;
import java.util.ArrayList;
import com.qxl.phone.currencyconverter.ui.main.AppSharedViewModel;


public class ConversionFragment extends Fragment {
    private MainViewModel viewModel;
    private AutoCompleteTextView spinnerFrom, spinnerTo;
    private TextInputEditText editAmount;
    private TextView textResult, textRate;
    private MaterialButton btnConvert, btnSwap;
    private MaterialCardView resultCard;
    private CircularProgressIndicator progressIndicator;
    private List<Currency> currencyList;
    private ArrayAdapter<String> adapter;
    private static final String ARG_TO_CURRENCY = "to_currency_code";
    private String toCurrencyCodeArg;

    public static ConversionFragment newInstance(String toCurrencyCode) {
        ConversionFragment fragment = new ConversionFragment();
        Bundle args = new Bundle();
        args.putString(ARG_TO_CURRENCY, toCurrencyCode);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_conversion, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
        AppSharedViewModel sharedViewModel = new ViewModelProvider(requireActivity()).get(AppSharedViewModel.class);
        if (getArguments() != null) {
            toCurrencyCodeArg = getArguments().getString(ARG_TO_CURRENCY);
        }
        spinnerFrom = view.findViewById(R.id.spinner_from);
        spinnerTo = view.findViewById(R.id.spinner_to);
        editAmount = view.findViewById(R.id.edit_amount);
        textResult = view.findViewById(R.id.text_result);
        textRate = view.findViewById(R.id.text_rate);
        btnConvert = view.findViewById(R.id.btn_convert);
        btnSwap = view.findViewById(R.id.btn_swap);
        resultCard = view.findViewById(R.id.result_card);
        progressIndicator = view.findViewById(R.id.progress_indicator);

        currencyList = viewModel.getCurrencyList();

        adapter = new ArrayAdapter<>(getContext(),
                android.R.layout.simple_dropdown_item_1line,
                getCurrencyDisplayList(currencyList));
        
        spinnerFrom.setAdapter(adapter);
        spinnerTo.setAdapter(adapter);

        spinnerFrom.setThreshold(1);
        spinnerTo.setThreshold(1);

        if (currencyList.size() > 1) {
            if (toCurrencyCodeArg == null) {
                int fromIndex = -1, toIndex = -1;
                for (int i = 0; i < currencyList.size(); i++) {
                    if ("USD".equals(currencyList.get(i).code)) fromIndex = i;
                    if ("CNY".equals(currencyList.get(i).code)) toIndex = i;
                }
                if (fromIndex == -1) fromIndex = 0;
                if (toIndex == -1) toIndex = 1;
                spinnerFrom.setText(getCurrencyDisplay(currencyList.get(fromIndex)), false);
                spinnerTo.setText(getCurrencyDisplay(currencyList.get(toIndex)), false);
            } else {
                for (int i = 0; i < currencyList.size(); i++) {
                    if ("USD".equals(currencyList.get(i).code)) {
                        spinnerFrom.setText(getCurrencyDisplay(currencyList.get(i)), false);
                        break;
                    }
                }

                if (spinnerFrom.getText().toString().isEmpty() && currencyList.size() > 0) {
                    spinnerFrom.setText(getCurrencyDisplay(currencyList.get(0)), false);
                }

                for (int i = 0; i < currencyList.size(); i++) {
                    if (toCurrencyCodeArg.equals(currencyList.get(i).code)) {
                        spinnerTo.setText(getCurrencyDisplay(currencyList.get(i)), false);
                        break;
                    }
                }
            }
        }

        spinnerFrom.setOnClickListener(v -> showCurrencySelectionDialog(true));
        spinnerTo.setOnClickListener(v -> showCurrencySelectionDialog(false));

        spinnerFrom.setOnItemClickListener((parent, listview, position, id) -> {
            String selectedCurrency = currencyList.get(position).code;
            String toCurrency = getSelectedCurrencyCode(spinnerTo.getText().toString(), currencyList);
            if (selectedCurrency.equals(toCurrency)) {
                for (int i = 0; i < currencyList.size(); i++) {
                    if (i != position) {
                        spinnerTo.setText(getCurrencyDisplay(currencyList.get(i)), false);
                        break;
                    }
                }
            }
        });

        spinnerTo.setOnItemClickListener((parent, listview, position, id) -> {
            String selectedCurrency = currencyList.get(position).code;
            String fromCurrency = getSelectedCurrencyCode(spinnerFrom.getText().toString(), currencyList);
            if (selectedCurrency.equals(fromCurrency)) {
                for (int i = 0; i < currencyList.size(); i++) {
                    if (i != position) {
                        spinnerFrom.setText(getCurrencyDisplay(currencyList.get(i)), false);
                        break;
                    }
                }
            }
        });

        viewModel.getLiveRate().observe(getViewLifecycleOwner(), rate -> {
            if (rate != null) {
                double amount = 0;
                try { 
                    amount = Double.parseDouble(editAmount.getText().toString()); 
                } catch (Exception ignore) {}
                
                if (amount > 0) {
                    textResult.setText(rate.getFormattedResult(amount));
                    textRate.setText(rate.getFormattedRate());
                    resultCard.setVisibility(View.VISIBLE);

                    viewModel.saveConversionHistory(rate.fromCurrency, rate.toCurrency, 
                                                  amount, rate.convert(amount), rate.rate);
                }
            }
        });
        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), error -> {
            if (error != null) {
                textResult.setText(error);
                resultCard.setVisibility(View.VISIBLE);
            }
        });

        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            progressIndicator.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            btnConvert.setEnabled(!isLoading);
        });

        btnConvert.setOnClickListener(v -> {
            String fromDisplay = spinnerFrom.getText().toString();
            String toDisplay = spinnerTo.getText().toString();
            String amountText = editAmount.getText().toString();
            
            if (fromDisplay.isEmpty() || toDisplay.isEmpty()) {
                textResult.setText(getString(R.string.please_select_currency));
                resultCard.setVisibility(View.VISIBLE);
                return;
            }

            if (amountText.isEmpty()) {
                textResult.setText(getString(R.string.please_enter_amount));
                resultCard.setVisibility(View.VISIBLE);
                return;
            }
            
            try {
                double amount = Double.parseDouble(amountText);
                String fromCurrency = getSelectedCurrencyCode(fromDisplay, currencyList);
                String toCurrency = getSelectedCurrencyCode(toDisplay, currencyList);
                
                if (fromCurrency.isEmpty() || toCurrency.isEmpty()) {
                    textResult.setText(getString(R.string.currency_selection_invalid));
                    resultCard.setVisibility(View.VISIBLE);
                    return;
                }

                viewModel.fetchRate(fromCurrency, toCurrency);
            } catch (NumberFormatException e) {
                textResult.setText(getString(R.string.invalid_amount));
                resultCard.setVisibility(View.VISIBLE);
            }
        });

        btnSwap.setOnClickListener(v -> {
            String fromDisplay = spinnerFrom.getText().toString();
            String toDisplay = spinnerTo.getText().toString();
            spinnerFrom.setText(toDisplay, false);
            spinnerTo.setText(fromDisplay, false);
        });

        AppBarLayout appBarLayout = view.findViewById(R.id.appbar);
        final View titleLayout = view.findViewById(R.id.title_layout);
        final int startColor = getResources().getColor(R.color.primary);
        final int endColor = getResources().getColor(R.color.background);
        final GradientDrawable gradient = new GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            new int[]{startColor, endColor}
        );
        titleLayout.setBackground(gradient);
        appBarLayout.addOnOffsetChangedListener(new AppBarLayout.OnOffsetChangedListener() {
            @Override
            public void onOffsetChanged(AppBarLayout appBarLayout, int verticalOffset) {
                int totalScrollRange = appBarLayout.getTotalScrollRange();
                float fraction = Math.abs(verticalOffset * 1f / totalScrollRange);
                int newStart = blendColors(startColor, endColor, fraction);
                int newEnd = blendColors(endColor, startColor, fraction);
                gradient.setColors(new int[]{newStart, newEnd});
            }
        });

        sharedViewModel.getFavoriteChanged().observe(getViewLifecycleOwner(), changed -> {
            if (Boolean.TRUE.equals(changed)) {
                currencyList = viewModel.getCurrencyList();
                adapter.clear();
                adapter.addAll(getCurrencyDisplayList(currencyList));
                adapter.notifyDataSetChanged();
            }
        });
    }

    private int blendColors(int from, int to, float ratio) {
        int alpha = (int) (Color.alpha(from) * (1 - ratio) + Color.alpha(to) * ratio);
        int red = (int) (Color.red(from) * (1 - ratio) + Color.red(to) * ratio);
        int green = (int) (Color.green(from) * (1 - ratio) + Color.green(to) * ratio);
        int blue = (int) (Color.blue(from) * (1 - ratio) + Color.blue(to) * ratio);
        return Color.argb(alpha, red, green, blue);
    }

    private List<String> getCurrencyDisplayList(List<Currency> currencies) {
        List<String> displayList = new ArrayList<>();
        for (Currency currency : currencies) {
            displayList.add(getCurrencyDisplay(currency));
        }
        return displayList;
    }

    private String getCurrencyDisplay(Currency currency) {
        String localizedName = getLocalizedCurrencyName(currency.code);
        return currency.code + " - " + localizedName;
    }

    private String getLocalizedCurrencyName(String code) {
        switch (code) {
            case "USD": return getString(R.string.currency_usd);
            case "EUR": return getString(R.string.currency_eur);
            case "CNY": return getString(R.string.currency_cny);
            case "JPY": return getString(R.string.currency_jpy);
            case "GBP": return getString(R.string.currency_gbp);
            case "CAD": return getString(R.string.currency_cad);
            case "AUD": return getString(R.string.currency_aud);
            case "CHF": return getString(R.string.currency_chf);
            case "HKD": return getString(R.string.currency_hkd);
            case "KRW": return getString(R.string.currency_krw);
            case "SGD": return getString(R.string.currency_sgd);
            case "NZD": return getString(R.string.currency_nzd);
            case "SEK": return getString(R.string.currency_sek);
            case "NOK": return getString(R.string.currency_nok);
            case "DKK": return getString(R.string.currency_dkk);
            case "PLN": return getString(R.string.currency_pln);
            case "CZK": return getString(R.string.currency_czk);
            case "HUF": return getString(R.string.currency_huf);
            case "RUB": return getString(R.string.currency_rub);
            case "TRY": return getString(R.string.currency_try);
            case "BRL": return getString(R.string.currency_brl);
            case "MXN": return getString(R.string.currency_mxn);
            case "INR": return getString(R.string.currency_inr);
            case "THB": return getString(R.string.currency_thb);
            case "MYR": return getString(R.string.currency_myr);
            case "IDR": return getString(R.string.currency_idr);
            case "PHP": return getString(R.string.currency_php);
            case "VND": return getString(R.string.currency_vnd);
            case "TWD": return getString(R.string.currency_twd);
            default:
                // 如果没有找到本地化名称，尝试从currencyList中查找englishName，否则返回货币代码
                for (Currency currency : currencyList) {
                    if (currency.code.equals(code)) {
                        return currency.englishName != null ? currency.englishName : code;
                    }
                }
                return code;
        }
    }

    private String getSelectedCurrencyCode(String displayText, List<Currency> currencies) {
        for (Currency currency : currencies) {
            if (displayText.contains(currency.code)) {
                return currency.code;
            }
        }
        return "";
    }

    private void showCurrencySelectionDialog(boolean isFromCurrency) {
        String title = isFromCurrency ? getString(R.string.select_source_currency) : getString(R.string.select_target_currency);
        CurrencySelectionDialog dialog = CurrencySelectionDialog.newInstance(currencyList, title);
        
        dialog.setOnCurrencySelectedListener(currency -> {
            String selectedDisplay = getCurrencyDisplay(currency);
            String selectedCode = currency.code;
            
            if (isFromCurrency) {
                String toCurrency = getSelectedCurrencyCode(spinnerTo.getText().toString(), currencyList);
                if (selectedCode.equals(toCurrency)) {
                    for (Currency c : currencyList) {
                        if (!c.code.equals(selectedCode)) {
                            spinnerTo.setText(getCurrencyDisplay(c), false);
                            break;
                        }
                    }
                }
                spinnerFrom.setText(selectedDisplay, false);
            } else {
                String fromCurrency = getSelectedCurrencyCode(spinnerFrom.getText().toString(), currencyList);
                if (selectedCode.equals(fromCurrency)) {
                    for (Currency c : currencyList) {
                        if (!c.code.equals(selectedCode)) {
                            spinnerFrom.setText(getCurrencyDisplay(c), false);
                            break;
                        }
                    }
                }
                spinnerTo.setText(selectedDisplay, false);
            }
        });
        
        dialog.show(getChildFragmentManager(), "CurrencySelectionDialog");
    }
}
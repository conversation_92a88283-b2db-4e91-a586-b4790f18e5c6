package com.qxl.phone.currencyconverter.ui.main;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class AppSharedViewModel extends ViewModel {
    private final MutableLiveData<Boolean> cacheCleared = new MutableLiveData<>();
    private final MutableLiveData<Boolean> favoriteChanged = new MutableLiveData<>();
    private final MutableLiveData<Boolean> historyChanged = new MutableLiveData<>();

    public void notifyCacheCleared() {
        cacheCleared.setValue(true);
    }

    public LiveData<Boolean> getCacheCleared() {
        return cacheCleared;
    }

    public void notifyFavoriteChanged() {
        favoriteChanged.setValue(true);
    }

    public LiveData<Boolean> getFavoriteChanged() {
        return favoriteChanged;
    }

    public void notifyHistoryChanged() {
        historyChanged.setValue(true);
    }

    public LiveData<Boolean> getHistoryChanged() {
        return historyChanged;
    }
} 
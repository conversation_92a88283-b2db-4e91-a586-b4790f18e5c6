<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Material Design 3 Button Styles -->
    <style name="Widget.Material3.Button" parent="Widget.Material3.Button" tools:ignore="ResourceCycle">
        <item name="android:textColor">@color/on_primary</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">@dimen/sw_dp_28</item>
    </style>

    <style name="Widget.Material3.Button.IconButton" parent="Widget.Material3.Button.IconButton" tools:ignore="ResourceCycle">
        <item name="android:textColor">@color/primary</item>
        <item name="backgroundTint">@color/surface</item>
        <item name="cornerRadius">@dimen/sw_dp_24</item>
        <item name="iconTint">@color/primary</item>
    </style>

    <style name="Widget.Material3.Button.IconButton.Filled" parent="Widget.Material3.Button.IconButton" tools:ignore="ResourceCycle">
        <item name="android:textColor">@color/on_primary</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">@dimen/sw_dp_28</item>
        <item name="iconTint">@color/on_primary</item>
        <item name="iconGravity">textStart</item>
        <item name="iconPadding">@dimen/sw_dp_0</item>
    </style>

    <style name="Widget.Material3.Button.TextButton" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/primary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="cornerRadius">@dimen/sw_dp_20</item>
    </style>

    <style name="Widget.Material3.Button.OutlinedButton" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/primary</item>
        <item name="backgroundTint">@android:color/transparent</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">@dimen/sw_dp_1</item>
        <item name="cornerRadius">@dimen/sw_dp_20</item>
    </style>

    <!-- TextInputLayout Styles -->
    <style name="Widget.Material3.TextInputLayout.OutlinedBox" parent="Widget.Material3.TextInputLayout.OutlinedBox" tools:ignore="ResourceCycle">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
        <item name="startIconTint">@color/primary</item>
        <item name="endIconTint">@color/primary</item>
    </style>

    <!-- Card Styles -->
    <style name="Widget.Material3.CardView" parent="Widget.Material3.CardView" tools:ignore="ResourceCycle">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardElevation">@dimen/sw_dp_4</item>
        <item name="cardCornerRadius">@dimen/sw_dp_16</item>
    </style>

    <!-- Switch Styles -->
    <style name="Widget.Material3.Switch" parent="Widget.Material3.Switch" tools:ignore="ResourceCycle">
        <item name="thumbTint">@color/primary</item>
        <item name="trackTint">@color/primary</item>
    </style>

    <style name="Widget.Currencyconverter.Button.Convert" parent="Widget.Material3.Button">
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
        <item name="android:paddingStart">@dimen/sw_dp_0</item>
        <item name="android:paddingEnd">@dimen/sw_dp_0</item>
        <item name="iconPadding">@dimen/sw_dp_0</item>
        <item name="iconGravity">textStart</item>
    </style>


</resources>
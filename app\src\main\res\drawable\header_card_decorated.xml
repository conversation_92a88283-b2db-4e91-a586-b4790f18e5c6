<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外发光层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="@dimen/sw_dp_200"
                android:centerX="0.5"
                android:centerY="0.5"
                android:startColor="#40FFFFFF"
                android:endColor="#00FFFFFF" />
            <corners android:radius="@dimen/sw_dp_20" />
            <padding
                android:bottom="@dimen/sw_dp_8"
                android:left="@dimen/sw_dp_8"
                android:right="@dimen/sw_dp_8"
                android:top="@dimen/sw_dp_8" />
        </shape>
    </item>
    
    <!-- 主背景层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/primary"
                android:endColor="@color/tertiary"
                android:centerColor="@color/secondary" />
            <corners android:radius="@dimen/sw_dp_20" />
        </shape>
    </item>
    
    <!-- 装饰线条 - 右上角 -->
    <item android:gravity="top|end">
        <shape android:shape="rectangle">
            <size android:width="@dimen/sw_dp_60" android:height="@dimen/sw_dp_2" />
            <solid android:color="#40FFFFFF" />
            <corners android:radius="@dimen/sw_dp_1" />
        </shape>
    </item>
    
    <!-- 装饰线条 - 左下角 -->
    <item android:gravity="bottom|start">
        <shape android:shape="rectangle">
            <size android:width="@dimen/sw_dp_40" android:height="@dimen/sw_dp_2" />
            <solid android:color="#40FFFFFF" />
            <corners android:radius="@dimen/sw_dp_1" />
        </shape>
    </item>
    
    <!-- 装饰圆点 - 左上角 -->
    <item android:top="@dimen/sw_dp_16" android:left="@dimen/sw_dp_16">
        <shape android:shape="oval">
            <size android:width="@dimen/sw_dp_8" android:height="@dimen/sw_dp_8" />
            <solid android:color="#40FFFFFF" />
        </shape>
    </item>
    
    <!-- 装饰圆点 - 右下角 -->
    <item android:bottom="@dimen/sw_dp_16" android:right="@dimen/sw_dp_16">
        <shape android:shape="oval">
            <size android:width="@dimen/sw_dp_6" android:height="@dimen/sw_dp_6" />
            <solid android:color="#40FFFFFF" />
        </shape>
    </item>
</layer-list> 
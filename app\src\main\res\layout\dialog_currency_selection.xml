<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/sw_dp_16">

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/sw_dp_16"
        android:hint="Search for currency"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/search_edit_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <ListView
        android:id="@+id/currency_list"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sw_dp_200"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="@dimen/sw_dp_0_5" />

</LinearLayout>
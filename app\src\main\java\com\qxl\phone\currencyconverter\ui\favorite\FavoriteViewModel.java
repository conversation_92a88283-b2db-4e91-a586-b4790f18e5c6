package com.qxl.phone.currencyconverter.ui.favorite;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.qxl.phone.currencyconverter.data.AppDatabase;
import com.qxl.phone.currencyconverter.model.FavoriteCurrency;
import com.qxl.phone.currencyconverter.util.ExchangeRateApi;
import com.qxl.phone.currencyconverter.util.AppExecutors;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class FavoriteViewModel extends AndroidViewModel {
    private final MutableLiveData<List<FavoriteCurrency>> favoriteListLiveData = new MutableLiveData<>();
    private final MutableLiveData<Map<String, Double>> ratesLiveData = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final ExchangeRateApi api;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static final String BASE_CURRENCY = "CNY";
    private Map<String, Double> ratesMap = new HashMap<>();

    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    public LiveData<Boolean> getIsLoading() { return isLoading; }

    public FavoriteViewModel(@NonNull Application application) {
        super(application);
        api = new ExchangeRateApi(application);
        favoriteListLiveData.setValue(new ArrayList<>());
        scheduler.scheduleAtFixedRate(this::updateRates, 15, 15, TimeUnit.MINUTES);
    }

    public LiveData<List<FavoriteCurrency>> getFavoriteListLiveData() {
        return favoriteListLiveData;
    }

    public LiveData<Map<String, Double>> getRatesLiveData() {
        return ratesLiveData;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public void updateRates() {
        isLoading.postValue(true);
        List<FavoriteCurrency> favorites = favoriteListLiveData.getValue();
        if (favorites != null) {
            updateRatesForFavorites(favorites);
        } else {
            isLoading.postValue(false);
        }
    }

    private void updateRatesForFavorites(List<FavoriteCurrency> favorites) {
        if (favorites.isEmpty()) {
            ratesLiveData.postValue(new HashMap<>());
            isLoading.postValue(false);
            return;
        }
        Map<String, Double> rates = new HashMap<>();
        int[] finishedCount = {0};
        for (FavoriteCurrency favorite : favorites) {
            if (BASE_CURRENCY.equals(favorite.getCurrencyCode())) {
                rates.put(favorite.getCurrencyCode(), 1.0);
                finishedCount[0]++;
                if (finishedCount[0] == favorites.size()) {
                    isLoading.postValue(false);
                    loadFavorites();
                }
                continue;
            }
            api.getExchangeRate(favorite.getCurrencyCode(), BASE_CURRENCY, new ExchangeRateApi.RateCallback() {
                @Override
                public void onSuccess(com.qxl.phone.currencyconverter.model.ExchangeRate rate) {
                    double rounded = Math.round(rate.rate * 10000.0) / 10000.0;
                    rates.put(favorite.getCurrencyCode(), rounded);
                    ratesMap.put(favorite.getCurrencyCode(), rounded);
                    Executors.newSingleThreadExecutor().execute(() -> {
                        AppDatabase db = AppDatabase.getInstance(getApplication());
                        db.favoriteDao().updateLastRate(favorite.getCurrencyCode(), rounded);
                    });
                    finishedCount[0]++;
                    if (finishedCount[0] == favorites.size()) {
                        isLoading.postValue(false);
                        loadFavorites();
                    }
                }
                @Override
                public void onError(String error) {
                    rates.put(favorite.getCurrencyCode(), null);
                    finishedCount[0]++;
                    if (finishedCount[0] == favorites.size()) {
                        isLoading.postValue(false);
                        loadFavorites();
                    }
                }
            });
        }
    }

    void loadFavorites() {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase db = AppDatabase.getInstance(getApplication());
            List<FavoriteCurrency> favorites = db.favoriteDao().getAll();
            favoriteListLiveData.postValue(favorites);
            updateRatesForFavorites(favorites);
        });
    }

    public void addToFavorites(String code, String name, String englishName) {
        double rate = ratesMap.get(code) != null ? ratesMap.get(code) : 0.0;
        AppExecutors.DB_EXECUTOR.execute(() -> {
            AppDatabase db = AppDatabase.getInstance(getApplication());
            FavoriteCurrency favorite = new FavoriteCurrency(code, name, "", "CNY");
            favorite.setLastRate(rate);
            db.favoriteDao().insertFavorite(favorite);
        });
    }

    public void removeFromFavorites(String code) {
        AppExecutors.DB_EXECUTOR.execute(() -> {
            AppDatabase db = AppDatabase.getInstance(getApplication());
            db.favoriteDao().deleteFavoriteByCode(code);
        });
    }

    public void clearAllFavorites() {
        AppExecutors.DB_EXECUTOR.execute(() -> {
            AppDatabase db = AppDatabase.getInstance(getApplication());
            db.favoriteDao().deleteAllFavorites();
        });
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        scheduler.shutdown();
    }
}
package com.qxl.phone.currencyconverter.ui.favorite.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ListAdapter;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.qxl.phone.currencyconverter.R;
import com.qxl.phone.currencyconverter.model.FavoriteCurrency;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class FavoriteAdapter extends ListAdapter<FavoriteCurrency, FavoriteAdapter.FavoriteViewHolder> {
    
    private OnFavoriteClickListener listener;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
    
    public interface OnFavoriteClickListener {
        void onFavoriteClick(FavoriteCurrency favorite);
        void onRemoveFavorite(FavoriteCurrency favorite);
    }
    
    public FavoriteAdapter() {
        super(new DiffUtil.ItemCallback<FavoriteCurrency>() {
            @Override
            public boolean areItemsTheSame(@NonNull FavoriteCurrency oldItem, @NonNull FavoriteCurrency newItem) {
                return oldItem.getCurrencyCode().equals(newItem.getCurrencyCode());
            }
            @Override
            public boolean areContentsTheSame(@NonNull FavoriteCurrency oldItem, @NonNull FavoriteCurrency newItem) {
                return oldItem.equals(newItem);
            }
        });
    }
    
    public void setOnFavoriteClickListener(OnFavoriteClickListener listener) {
        this.listener = listener;
    }
    
    public void setFavorites(List<FavoriteCurrency> favorites) {
        if (favorites != null) {
            List<FavoriteCurrency> sorted = new ArrayList<>(favorites);
            Collections.sort(sorted, new Comparator<FavoriteCurrency>() {
                @Override
                public int compare(FavoriteCurrency o1, FavoriteCurrency o2) {
                    return Long.compare(o2.getTimestamp(), o1.getTimestamp());
                }
            });
            submitList(sorted);
        } else {
            submitList(new ArrayList<>());
        }
    }
    
    @NonNull
    @Override
    public FavoriteViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_favorite, parent, false);
        return new FavoriteViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FavoriteViewHolder holder, int position) {
        holder.bind(getItem(position));
    }
    
    @Override
    public int getItemCount() {
        return getCurrentList().size();
    }
    
    class FavoriteViewHolder extends RecyclerView.ViewHolder {
        private TextView currencyCode;
        private TextView currencyName;
        private TextView exchangeRate;
        private TextView lastUpdated;
        private ImageView removeButton;
        
        public FavoriteViewHolder(@NonNull View itemView) {
            super(itemView);
            currencyCode = itemView.findViewById(R.id.currency_code);
            currencyName = itemView.findViewById(R.id.currency_name);
            exchangeRate = itemView.findViewById(R.id.exchange_rate);
            lastUpdated = itemView.findViewById(R.id.last_updated);
            removeButton = itemView.findViewById(R.id.remove_favorite);
        }
        
        public void bind(FavoriteCurrency favorite) {
            currencyCode.setText(favorite.getCurrencyCode());
            currencyName.setText(favorite.getCurrencyName());

            if (favorite.getLastRate() > 0) {
                exchangeRate.setText(String.format(exchangeRate.getContext().getString(R.string.exchange_rate_format),
                        favorite.getLastRate(),
                        favorite.getCurrencyCode()));
                exchangeRate.setVisibility(View.VISIBLE);
            } else {
                exchangeRate.setVisibility(View.GONE);
            }

            lastUpdated.setText(String.format(lastUpdated.getContext().getString(R.string.add_time_format),
                dateFormat.format(new Date(favorite.getTimestamp()))));
            
            removeButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onRemoveFavorite(favorite);
                }
            });
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onFavoriteClick(favorite);
                }
            });
        }
    }
}
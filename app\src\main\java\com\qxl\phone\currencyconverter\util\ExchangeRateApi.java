package com.qxl.phone.currencyconverter.util;

import android.content.Context;
import android.util.Log;
import com.qxl.phone.currencyconverter.model.ExchangeRate;
import okhttp3.*;
import org.json.JSONObject;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import org.json.JSONException;
import java.util.Iterator;
import java.util.Map;
import java.util.HashMap;

public class ExchangeRateApi {
    private static final String TAG = "ExchangeRateApi";
    private static final String PREFS_NAME = "exchange_rate_cache";
    private static final String KEY_RATES_JSON = "rates_json";
    private static final String KEY_RATES_TIME = "rates_time";
    private static final long CACHE_EXPIRE_MILLIS = 10 * 60 * 1000; // 10分钟

    private final OkHttpClient client;
    private final Context context;

    public ExchangeRateApi(Context context) {
        this.context = context.getApplicationContext();
        this.client = new OkHttpClient();
    }

    public interface RateCallback {
        void onSuccess(ExchangeRate rate);
        void onError(String error);
    }

    // 新增：一次性拉取所有币种对USD汇率（基于xxapi.cn）
    public interface AllRatesCallback {
        void onSuccess(Map<String, Double> rates);
        void onError(String error);
    }

    // 保存汇率到本地
    private void saveRatesToCache(String json) {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
               .edit().putString(KEY_RATES_JSON, json).apply();
    }

    // 从本地读取汇率
    private String getRatesFromCache() {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                      .getString(KEY_RATES_JSON, null);
    }

    // 获取缓存时间戳
    private long getRatesCacheTime() {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                      .getLong(KEY_RATES_TIME, 0);
    }

    // 判断缓存是否过期
    private boolean isCacheExpired() {
        long cacheTime = getRatesCacheTime();
        return System.currentTimeMillis() - cacheTime > CACHE_EXPIRE_MILLIS;
    }

    // 修改getAllRatesToCNY方法
    public void getAllRatesToCNY(AllRatesCallback callback) {
        String url = "https://v2.xxapi.cn/api/allrates";
        Request request = new Request.Builder()
                .url(url)
                .addHeader("User-Agent", "CurrencyConverter/1.0.0")
                .addHeader("Accept", "application/json")
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String cachedJson = getRatesFromCache();
                if (cachedJson != null) {
                    try {
                        JSONObject json = new JSONObject(cachedJson);
                        if (json.getInt("code") == 200) {
                            JSONObject data = json.getJSONObject("data");
                            JSONObject rates = data.getJSONObject("rates");
                            Map<String, Double> result = new HashMap<>();
                            Iterator<String> keys = rates.keys();
                            while (keys.hasNext()) {
                                String code = keys.next();
                                JSONObject currency = rates.getJSONObject(code);
                                double rate = currency.getDouble("rate");
                                result.put(code, rate);
                            }
                            callback.onSuccess(result);
                            return;
                        }
                    } catch (Exception ex) {
                        // ignore
                    }
                }
                callback.onError("The network connection failed and no local cache was available");
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful()) {
                        String jsonData = response.body().string();
                        try {
                            JSONObject json = new JSONObject(jsonData);
                            if (json.getInt("code") == 200) {
                                // 保存到本地
                                saveRatesToCache(jsonData);
                                JSONObject data = json.getJSONObject("data");
                                JSONObject rates = data.getJSONObject("rates");
                                Map<String, Double> result = new HashMap<>();
                                Iterator<String> keys = rates.keys();
                                while (keys.hasNext()) {
                                    String code = keys.next();
                                    JSONObject currency = rates.getJSONObject(code);
                                    double rate = currency.getDouble("rate");
                                    result.put(code, rate);
                                }
                                callback.onSuccess(result);
                            } else {
                                String errorMsg = json.optString("msg", "未知错误");
                                callback.onError("API returns an error: " + errorMsg);
                            }
                        } catch (JSONException e) {
                            callback.onError("Parsing data failed: " + e.getMessage());
                        }
                    } else {
                        callback.onError("Failed to get the exchange rate, HTTP status code: " + response.code());
                    }
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    public void getExchangeRate(String fromCurrency, String toCurrency, RateCallback callback) {
        getAllRatesToCNY(new AllRatesCallback() {
            @Override
            public void onSuccess(Map<String, Double> allRates) {
                try {
                    if (allRates.containsKey(fromCurrency) && allRates.containsKey(toCurrency)) {
                        double fromRate = allRates.get(fromCurrency);
                        double toRate = allRates.get(toCurrency);

                        double exchangeRate = toRate / fromRate;
                        
                        ExchangeRate rate = new ExchangeRate(fromCurrency, toCurrency, exchangeRate, 
                            String.valueOf(System.currentTimeMillis()));
                        callback.onSuccess(rate);
                    } else {
                        callback.onError("Unsupported Currency Codes: " + fromCurrency + " or " + toCurrency);
                    }
                } catch (Exception e) {
                    callback.onError("Failed to calculate the exchange rate: " + e.getMessage());
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
}
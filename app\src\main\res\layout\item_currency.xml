<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/sw_dp_8"
    android:layout_marginVertical="@dimen/sw_dp_4"
    app:cardCornerRadius="@dimen/sw_dp_12"
    app:cardElevation="@dimen/sw_dp_2"
    app:cardBackgroundColor="@color/surface"
    android:foreground="@null"
    android:clickable="false"
    android:focusable="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/sw_dp_16"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/text_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textSize="@dimen/sw_dp_18"
            android:textColor="@color/primary"
            android:background="@drawable/currency_code_background"
            android:paddingHorizontal="@dimen/sw_dp_12"
            android:paddingVertical="@dimen/sw_dp_6"
            android:layout_marginEnd="@dimen/sw_dp_16" />

        <!-- 货币信息 -->
        <LinearLayout
            android:layout_width="@dimen/sw_dp_0"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sw_dp_16"
                android:textColor="@color/on_surface"
                android:layout_marginBottom="@dimen/sw_dp_2" />

            <TextView
                android:id="@+id/text_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/on_surface_variant" />
            <!-- 汇率显示 -->
            <TextView
                android:id="@+id/text_rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/primary"
                android:layout_marginTop="@dimen/sw_dp_2"
                android:visibility="visible" />
        </LinearLayout>

        <!-- 收藏按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_favorite"
            android:layout_width="@dimen/sw_dp_48"
            android:layout_height="@dimen/sw_dp_48"
            android:layout_marginStart="@dimen/sw_dp_8"
            app:icon="@drawable/ic_star_border"
            app:iconTint="@color/primary"
            app:iconSize="@dimen/sw_dp_24"
            style="@style/Widget.Material3.Button.IconButton" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
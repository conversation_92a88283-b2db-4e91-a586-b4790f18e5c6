<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="@dimen/sw_dp_30">

        <!-- 标题分区 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/sw_dp_24"
            android:paddingTop="@dimen/sw_dp_12"
            android:paddingBottom="@dimen/sw_dp_12">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="@dimen/sw_dp_28"
                    android:layout_height="@dimen/sw_dp_28"
                    android:src="@drawable/ic_star_filled"
                    app:tint="@color/primary"
                    android:layout_marginEnd="@dimen/sw_dp_12" />

                <TextView
                    android:layout_width="@dimen/sw_dp_0"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/favorite_currencies"
                    android:textSize="@dimen/sw_dp_22"
                    android:textStyle="bold"
                    android:textColor="@color/on_surface" />

                <TextView
                    android:id="@+id/favorite_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="@dimen/sw_dp_16"
                    android:textColor="@color/primary"
                    android:background="@drawable/count_badge"
                    android:paddingHorizontal="@dimen/sw_dp_8"
                    android:paddingVertical="@dimen/sw_dp_4" />
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/add_favorites_hint"
                android:textSize="@dimen/sw_dp_14"
                android:textColor="@color/on_surface_variant"
                android:layout_marginTop="@dimen/sw_dp_4" />

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/sw_dp_1"
                android:background="@color/outline_variant"
                android:layout_marginTop="@dimen/sw_dp_12"/>
        </LinearLayout>

        <!-- 收藏列表 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sw_dp_0"
            android:layout_weight="1"
            android:layout_marginHorizontal="@dimen/sw_dp_16"
            android:layout_marginBottom="@dimen/sw_dp_16">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/favorite_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingTop="@dimen/sw_dp_8"
                android:paddingBottom="@dimen/sw_dp_8" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/empty_favorites"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        android:padding="@dimen/sw_dp_32">
        <ImageView
            android:layout_width="@dimen/sw_dp_80"
            android:layout_height="@dimen/sw_dp_80"
            android:src="@drawable/ic_star_border"
            android:alpha="0.5"
            android:layout_marginBottom="@dimen/sw_dp_16" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_favorites"
            android:textSize="@dimen/sw_dp_18"
            android:textColor="@color/on_surface_variant"
            android:gravity="center"
            android:layout_marginBottom="@dimen/sw_dp_8" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/add_favorites_hint"
            android:textSize="@dimen/sw_dp_14"
            android:textColor="@color/on_surface_variant"
            android:gravity="center" />

    </LinearLayout>

</FrameLayout>
package com.qxl.phone.currencyconverter.ui.main;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import com.qxl.phone.currencyconverter.ui.list.CurrencyListFragment;
import com.qxl.phone.currencyconverter.ui.favorite.FavoriteFragment;
import com.qxl.phone.currencyconverter.ui.history.HistoryFragment;
import com.qxl.phone.currencyconverter.ui.settings.SettingsFragment;

public class MainPagerAdapter extends FragmentStateAdapter {
    public MainPagerAdapter(@NonNull FragmentActivity fa) {
        super(fa);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new ConversionFragment();
            case 1:
                return new CurrencyListFragment();
            case 2:
                return new FavoriteFragment();
            case 3:
                return new HistoryFragment();
            case 4:
                return new SettingsFragment();
            default:
                return new ConversionFragment();
        }
    }

    @Override
    public int getItemCount() {
        return 5;
    }
} 
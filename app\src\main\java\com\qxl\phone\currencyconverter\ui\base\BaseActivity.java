package com.qxl.phone.currencyconverter.ui.base;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.View;

import androidx.activity.EdgeToEdge;
import androidx.activity.SystemBarStyle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewbinding.ViewBinding;

import com.qxl.phone.currencyconverter.language.MultiLanguages;


/**
 * 基础 Activity 类，实现 ViewBinding
 *
 * @param <T> ViewBinding 类型
 */
public abstract class BaseActivity<T extends ViewBinding> extends AppCompatActivity {
    protected T binding;

    @Override
    protected void attachBaseContext(Context base) {
        try {
            super.attachBaseContext((base != null) ? MultiLanguages.attach(base) : null);
        } catch (Throwable exception) {
            super.attachBaseContext(base);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = getViewBinding();
        if (binding != null) {
            setContentView(binding.getRoot());
            initWindow(binding.getRoot());
        }
        initData(getIntent() != null ? getIntent().getExtras() : null);
        initView();
        initEvent();
        loadData();
    }

    public void initWindow(@NonNull View rootView) {
        EdgeToEdge.enable(this, SystemBarStyle.light(Color.TRANSPARENT, Color.TRANSPARENT));

        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            final Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, 0, systemBars.right, systemBars.bottom);
            return insets;
        });
    }

    /**
     * 初始化 ViewBinding
     *
     * @return ViewBinding 实例
     */
    protected abstract T getViewBinding();

    /**
     * 初始化视图
     */
    public void initView() {

    }

    /**
     * 初始化数据
     */
    public void initData(@Nullable Bundle extras) {

    }

    public void initEvent() {
    }

    public void loadData() {
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
} 
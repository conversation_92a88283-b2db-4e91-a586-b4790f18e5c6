package com.qxl.phone.currencyconverter;

import android.app.Application;
import android.content.Context;

import androidx.appcompat.app.AppCompatDelegate;

import com.qxl.phone.currencyconverter.data.PreferencesHelper;
import com.qxl.phone.currencyconverter.language.MultiLanguages;

public class CurrencyConverterApp extends Application {

    @Override
    protected void attachBaseContext(Context base) {
        try {
            super.attachBaseContext((base != null) ? MultiLanguages.attach(base) : null);
        } catch (Throwable ignored) {
            super.attachBaseContext(base);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        MultiLanguages.init(this);
        applyThemeSettings();
    }

    private void applyThemeSettings() {
        boolean isDarkMode = PreferencesHelper.isDarkMode(this);
        if (isDarkMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }
}
package com.qxl.phone.currencyconverter.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;

@Entity(tableName = "currencies")
public class Currency {
    @PrimaryKey(autoGenerate = true)
    public int id;
    public String code;
    public String englishName;
    public boolean favorite;
    public Currency(String code, String englishName, boolean favorite) {
        this.code = code;
        this.englishName = englishName;
        this.favorite = favorite;
    }

    @Ignore
    public Currency(String code, String englishName) {
        this.code = code;
        this.englishName = englishName;
        this.favorite = false;
    }
}
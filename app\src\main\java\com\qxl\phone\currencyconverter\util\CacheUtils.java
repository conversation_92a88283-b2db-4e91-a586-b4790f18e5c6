package com.qxl.phone.currencyconverter.util;


import com.qxl.phone.currencyconverter.data.enum2.Language;

public final class CacheUtils {
    private static final String REPORT_FIRST_SHOW_SPLASH = "REPORT_FIRST_SHOW_SPLASH";
    private static final String REPORT_FIRST_LANGUAGE_DONE_USE = "REPORT_FIRST_LANGUAGE_DONE_USE";
    private static final String REPORT_FIRST_SPLASH_ADVERT = "REPORT_FIRST_SPLASH_ADVERT";
    private static final String REPORT_FIRST_LANGUAGE_SHOW = "REPORT_FIRST_LANGUAGE_SHOW";
    private static final String CACHE_LANGUAGE_NAME = "CACHE_LANGUAGE_NAME";
    private static final String CACHE_SHOW_GUIDE = "CACHE_SHOW_GUIDE";

    /**
     * 当前语种
     */
    public static String getCurLanguageName() {
        return SPUtils.getInstance().getString(CACHE_LANGUAGE_NAME, Language.DEFAULT.name());
    }

    public static void setCurLanguageName(String value) {
        SPUtils.getInstance().put(CACHE_LANGUAGE_NAME, value);
    }


    /**
     * @return 是否首次展示开屏页上报
     */
    public static boolean isFirstSplashPageReport() {
        final boolean canReport = SPUtils.getInstance().getBoolean(REPORT_FIRST_SHOW_SPLASH, true);
        if (canReport) {
            SPUtils.getInstance().put(REPORT_FIRST_SHOW_SPLASH, false);
        }
        return canReport;
    }




    /**
     * @return 是否首次语言页点击完成按钮
     */
    public static boolean isFirstLanguageDoneUse() {
        final boolean isFirst = SPUtils.getInstance().getBoolean(REPORT_FIRST_LANGUAGE_DONE_USE, true);
        if (isFirst) {
            updateFirstLanguageDoneUse(false);
        }
        return isFirst;
    }

    public static void updateFirstLanguageDoneUse(boolean value) {
        SPUtils.getInstance().put(REPORT_FIRST_LANGUAGE_DONE_USE, value);
    }

    public static boolean isFirstLanguageShow() {
        return SPUtils.getInstance().getBoolean(REPORT_FIRST_LANGUAGE_SHOW, true);
    }


    public static void updateFirstLanguageShow(boolean value) {
        SPUtils.getInstance().put(REPORT_FIRST_LANGUAGE_SHOW, value);
    }

    public static boolean isFirstSplashAdvert() {
        final boolean isFirst = SPUtils.getInstance().getBoolean(REPORT_FIRST_SPLASH_ADVERT, true);
        if (isFirst) {
            SPUtils.getInstance().put(REPORT_FIRST_SPLASH_ADVERT, false);
        }
        return isFirst;
    }

    /**
     * 是否展示引导页
     */
    public static boolean isShowGuidePage() {
        return SPUtils.getInstance().getBoolean(CACHE_SHOW_GUIDE, true);
    }

    public static void setShowGuidePage(boolean value) {
        SPUtils.getInstance().put(CACHE_SHOW_GUIDE, value);
    }
}
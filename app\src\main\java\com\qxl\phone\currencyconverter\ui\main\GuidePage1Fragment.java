package com.qxl.phone.currencyconverter.ui.main;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.qxl.phone.currencyconverter.databinding.FragmentGuidePage1Binding;
import com.qxl.phone.currencyconverter.ui.base.BaseFragment;


public class GuidePage1Fragment extends BaseFragment<FragmentGuidePage1Binding> {

    @NonNull
    @Override
    public FragmentGuidePage1Binding initBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container) {
        return FragmentGuidePage1Binding.inflate(getLayoutInflater());
    }
}
